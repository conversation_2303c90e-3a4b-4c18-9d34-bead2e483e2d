const express = require('express');
const multer = require('multer');
const sharp = require('sharp');
const exifReader = require('exif-reader');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = process.env.PORT || 3000;

// 创建必要的目录
const uploadsDir = path.join(__dirname, 'uploads');
const outputDir = path.join(__dirname, 'output');
const publicDir = path.join(__dirname, 'public');

[uploadsDir, outputDir, publicDir].forEach(dir => {
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
    }
});

// 配置multer用于文件上传
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, uploadsDir);
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
    }
});

const fileFilter = (req, file, cb) => {
    // 只允许图片文件
    if (file.mimetype.startsWith('image/')) {
        cb(null, true);
    } else {
        cb(new Error('只允许上传图片文件！'), false);
    }
};

const upload = multer({ 
    storage: storage,
    fileFilter: fileFilter,
    limits: {
        fileSize: 10 * 1024 * 1024 // 10MB限制
    }
});

// 静态文件服务
app.use(express.static('public'));
app.use('/output', express.static('output'));

// 主页路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// 图片上传和处理路由
app.post('/upload', upload.single('image'), async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ error: '请选择一个图片文件' });
        }

        const inputPath = req.file.path;
        const outputFilename = 'processed-' + req.file.filename;
        const outputPath = path.join(outputDir, outputFilename);

        // 读取图片并提取EXIF信息
        const imageBuffer = fs.readFileSync(inputPath);
        const image = sharp(imageBuffer);
        const metadata = await image.metadata();

        let exifData = {};
        console.log('图片metadata:', {
            format: metadata.format,
            width: metadata.width,
            height: metadata.height,
            hasExif: !!metadata.exif,
            exifLength: metadata.exif ? metadata.exif.length : 0
        });

        if (metadata.exif) {
            try {
                // 使用exif-reader解析EXIF数据
                exifData = exifReader(metadata.exif);
                console.log('成功解析EXIF数据:', JSON.stringify(exifData, null, 2));
            } catch (error) {
                console.log('EXIF解析错误:', error.message);
                console.log('EXIF buffer前100字节:', metadata.exif.slice(0, 100));
            }
        } else {
            console.log('图片中没有EXIF数据');
        }

        // 处理图片并添加EXIF信息叠加层
        await processImageWithExif(inputPath, outputPath, exifData, metadata);

        // 清理上传的原始文件
        fs.unlinkSync(inputPath);

        res.json({
            success: true,
            exifData: formatExifData(exifData),
            outputImage: `/output/${outputFilename}`,
            originalName: req.file.originalname
        });

    } catch (error) {
        console.error('处理图片时出错:', error);
        res.status(500).json({ error: '处理图片时出错: ' + error.message });
    }
});

// 格式化EXIF数据用于显示
function formatExifData(exifData) {
    const formatted = {};

    if (!exifData) return formatted;

    // exif-reader返回的数据结构是数字键值对
    // 常用的EXIF标签对应的数字键：
    // 271: Make (相机品牌)
    // 272: Model (相机型号)
    // 33434: ExposureTime (曝光时间)
    // 33437: FNumber (光圈)
    // 34855: ISO
    // 37386: FocalLength (焦距)
    // 36867: DateTimeOriginal (拍摄时间)

    if (exifData['271']) formatted['相机品牌'] = exifData['271'];
    if (exifData['272']) formatted['相机型号'] = exifData['272'];
    if (exifData['36867']) formatted['拍摄时间'] = exifData['36867'];

    if (exifData['33434']) {
        const exposureTime = exifData['33434'];
        if (exposureTime < 1) {
            formatted['曝光时间'] = `1/${Math.round(1/exposureTime)}`;
        } else {
            formatted['曝光时间'] = `${exposureTime}s`;
        }
    }

    if (exifData['33437']) formatted['光圈'] = `f/${exifData['33437']}`;
    if (exifData['34855']) formatted['ISO'] = exifData['34855'];
    if (exifData['37386']) formatted['焦距'] = `${exifData['37386']}mm`;

    // GPS信息
    if (exifData.latitude && exifData.longitude) {
        const latRef = exifData['1'] || 'N';
        const lonRef = exifData['3'] || 'E';
        formatted['GPS坐标'] = `${exifData.latitude.toFixed(6)}°${latRef} ${exifData.longitude.toFixed(6)}°${lonRef}`;
    }

    return formatted;
}

// 处理图片并添加EXIF信息叠加层
async function processImageWithExif(inputPath, outputPath, exifData, metadata) {
    const image = sharp(inputPath);
    const { width, height } = metadata;
    
    // 创建EXIF信息文本
    const exifText = createExifText(exifData, metadata);
    
    // 创建文本叠加层
    const textOverlay = await createTextOverlay(exifText, width);
    
    // 合成图片
    await image
        .composite([{
            input: textOverlay,
            top: height - 120, // 距离底部120像素
            left: 20,
            blend: 'over'
        }])
        .jpeg({ quality: 90 })
        .toFile(outputPath);
}

// 创建EXIF信息文本
function createExifText(exifData, metadata) {
    let textLines = [];

    if (!exifData) return '无EXIF信息';

    // 相机信息
    if (exifData['271'] && exifData['272']) {
        textLines.push(`${exifData['271']} ${exifData['272']}`);
    }

    // 拍摄参数
    let params = [];
    if (exifData['37386']) params.push(`${exifData['37386']}mm`);
    if (exifData['33437']) params.push(`f/${exifData['33437']}`);
    if (exifData['33434']) {
        const exposureTime = exifData['33434'];
        if (exposureTime < 1) {
            params.push(`1/${Math.round(1/exposureTime)}`);
        } else {
            params.push(`${exposureTime}s`);
        }
    }
    if (exifData['34855']) params.push(`ISO${exifData['34855']}`);

    if (params.length > 0) {
        textLines.push(params.join(' '));
    }

    // 拍摄时间
    if (exifData['36867']) {
        textLines.push(exifData['36867']);
    }

    // GPS信息
    if (exifData.latitude && exifData.longitude) {
        const latRef = exifData['1'] || 'N';
        const lonRef = exifData['3'] || 'E';
        textLines.push(`${exifData.latitude.toFixed(6)}°${latRef} ${exifData.longitude.toFixed(6)}°${lonRef}`);
    }

    return textLines.join('\n');
}

// 创建文本叠加层
async function createTextOverlay(text, imageWidth) {
    const fontSize = Math.max(16, Math.floor(imageWidth / 50));
    const textWidth = Math.floor(imageWidth * 0.9);
    
    const svgText = `
        <svg width="${textWidth}" height="100">
            <defs>
                <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
                    <feDropShadow dx="1" dy="1" stdDeviation="2" flood-color="black" flood-opacity="0.8"/>
                </filter>
            </defs>
            <rect width="100%" height="100%" fill="rgba(0,0,0,0.6)" rx="5"/>
            <text x="10" y="25" font-family="Arial, sans-serif" font-size="${fontSize}" fill="white" filter="url(#shadow)">
                ${text.split('\n').map((line, index) => 
                    `<tspan x="10" dy="${index === 0 ? 0 : fontSize + 2}">${line}</tspan>`
                ).join('')}
            </text>
        </svg>
    `;
    
    return Buffer.from(svgText);
}

// 启动服务器
app.listen(PORT, () => {
    console.log(`服务器运行在 http://localhost:${PORT}`);
});
