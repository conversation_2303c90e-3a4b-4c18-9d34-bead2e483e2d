import {tagKeys, createDictionary} from '../tags.mjs'


// https://exiftool.org/TagNames/ICC_Profile.html

createDictionary(tagKeys, 'icc', [
	// Header Tags
	[4,  'ProfileCMMType'],
	[8,  'ProfileVersion'],
	[12, 'ProfileClass'],
	[16, 'ColorSpaceData'],
	[20, 'ProfileConnectionSpace'],
	[24, 'ProfileDateTime'],
	[36, 'ProfileFileSignature'],
	[40, 'PrimaryPlatform'],
	[44, 'CMMFlags'],
	[48, 'DeviceManufacturer'],
	[52, 'DeviceModel'],
	[56, 'DeviceAttributes'],
	[64, 'RenderingIntent'],
	[68, 'ConnectionSpaceIlluminant'],
	[80, 'ProfileCreator'],
	[84, 'ProfileID'],
	// Other Tags
	['Header', 'ProfileHeader'],
	['MS00',   'WCSProfiles'],
	['bTRC',   'BlueTRC'],
	['bXYZ',   'BlueMatrixColumn'],
	['bfd',    'UCRBG'],
	['bkpt',   'MediaBlackPoint'],
	['calt',   'CalibrationDateTime'],
	['chad',   'ChromaticAdaptation'],
	['chrm',   'Chromaticity'],
	['ciis',   'ColorimetricIntentImageState'],
	['clot',   'ColorantTableOut'],
	['clro',   'ColorantOrder'],
	['clrt',   'ColorantTable'],
	['cprt',   'ProfileCopyright'],
	['crdi',   'CRDInfo'],
	['desc',   'ProfileDescription'],
	['devs',   'DeviceSettings'],
	['dmdd',   'DeviceModelDesc'],
	['dmnd',   'DeviceMfgDesc'],
	['dscm',   'ProfileDescriptionML'],
	['fpce',   'FocalPlaneColorimetryEstimates'],
	['gTRC',   'GreenTRC'],
	['gXYZ',   'GreenMatrixColumn'],
	['gamt',   'Gamut'],
	['kTRC',   'GrayTRC'],
	['lumi',   'Luminance'],
	['meas',   'Measurement'],
	['meta',   'Metadata'],
	['mmod',   'MakeAndModel'],
	['ncl2',   'NamedColor2'],
	['ncol',   'NamedColor'],
	['ndin',   'NativeDisplayInfo'],
	['pre0',   'Preview0'],
	['pre1',   'Preview1'],
	['pre2',   'Preview2'],
	['ps2i',   'PS2RenderingIntent'],
	['ps2s',   'PostScript2CSA'],
	['psd0',   'PostScript2CRD0'],
	['psd1',   'PostScript2CRD1'],
	['psd2',   'PostScript2CRD2'],
	['psd3',   'PostScript2CRD3'],
	['pseq',   'ProfileSequenceDesc'],
	['psid',   'ProfileSequenceIdentifier'],
	['psvm',   'PS2CRDVMSize'],
	['rTRC',   'RedTRC'],
	['rXYZ',   'RedMatrixColumn'],
	['resp',   'OutputResponse'],
	['rhoc',   'ReflectionHardcopyOrigColorimetry'],
	['rig0',   'PerceptualRenderingIntentGamut'],
	['rig2',   'SaturationRenderingIntentGamut'],
	['rpoc',   'ReflectionPrintOutputColorimetry'],
	['sape',   'SceneAppearanceEstimates'],
	['scoe',   'SceneColorimetryEstimates'],
	['scrd',   'ScreeningDesc'],
	['scrn',   'Screening'],
	['targ',   'CharTarget'],
	['tech',   'Technology'],
	['vcgt',   'VideoCardGamma'],
	['view',   'ViewingConditions'],
	['vued',   'ViewingCondDesc'],
	['wtpt',   'MediaWhitePoint'],
])