{"name": "exifr", "version": "7.1.3", "description": "📷 The fastest and most versatile JavaScript EXIF reading library.", "author": "<PERSON>", "license": "MIT", "keywords": ["exif", "jpg", "jpeg", "png", "heif", "heic", "avif", "tiff", "xmp", "gps", "icc", "iptc", "jfif", "metadata", "orientation", "makernote"], "homepage": "https://mutiny.cz/exifr/", "bugs": "https://github.com/Mike<PERSON><PERSON>rik/exifr/issues", "repository": {"type": "git", "url": "https://github.com/Mike<PERSON>ovarik/exifr.git"}, "main": "./dist/full.umd.js", "module": "./dist/full.esm.mjs", "types": "index.d.ts", "scripts": {"test": "mocha \"test/**/*.spec.mjs\" --recursive --exclude test/bundle-mini.spec.mjs --exclude test/bundle-lite.spec.mjs --exclude test/bundle-full.spec.mjs", "test-bundles": "mocha test/bundle-mini.spec.mjs && mocha test/bundle-lite.spec.mjs && mocha test/bundle-full.spec.mjs", "test-full": "mocha test/bundle-full.spec.mjs", "test-lite": "mocha test/bundle-lite.spec.mjs", "test-mini": "mocha test/bundle-mini.spec.mjs", "coverage": "c8 npm test", "dev": "rollup -c rollup.config.js -w", "build-mini": "rollup -c rollup.config.js mini", "build-lite": "rollup -c rollup.config.js lite", "build-full": "rollup -c rollup.config.js full", "build": "rollup -c rollup.config.js", "prerelease": "npm run build"}, "devDependencies": {"@babel/core": "^7.6.4", "@babel/plugin-proposal-class-properties": "^7.5.5", "@babel/plugin-proposal-optional-chaining": "^7.11.0", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-arrow-functions": "^7.8.3", "@babel/plugin-transform-block-scoping": "^7.8.3", "@babel/plugin-transform-classes": "^7.8.6", "@babel/plugin-transform-computed-properties": "^7.8.3", "@babel/plugin-transform-destructuring": "^7.8.3", "@babel/plugin-transform-duplicate-keys": "^7.8.3", "@babel/plugin-transform-function-name": "^7.8.3", "@babel/plugin-transform-literals": "^7.8.3", "@babel/plugin-transform-parameters": "^7.8.7", "@babel/plugin-transform-shorthand-properties": "^7.8.3", "@babel/plugin-transform-spread": "^7.8.3", "@babel/plugin-transform-template-literals": "^7.8.3", "@babel/preset-env": "^7.6.3", "aurelia-script": "^1.5.2", "babel-plugin-transform-async-to-promises": "^0.8.15", "babel-plugin-transform-for-of-without-iterator": "^1.0.3", "c8": "^7.0.1", "chai": "^4.3.3", "coveralls": "^3.0.7", "decorate": "^1.0.0", "express": "^4.17.1", "mocha": "^8.3.0", "rollup": "^2.40.0", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-notify": "^1.0.1", "rollup-plugin-terser": "^7.0.2"}, "c8": {"exclude": ["src/file-readers/BlobReader.js", "src/file-readers/UrlFetcher.js", "src/util/debug.js", "benchmark/**.*", "debug/**.*", "examples/**.*", "dist/**.*", "test/**.*"], "reporter": ["html", "text", "text-summary"]}, "dependencies": {}}