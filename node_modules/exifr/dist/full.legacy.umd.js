!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define("exifr",["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).exifr={})}(this,(function(e){"use strict";function t(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function n(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function r(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e}function i(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function a(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}});var n=["prototype","__proto__","caller","arguments","length","name"];Object.getOwnPropertyNames(t).forEach((function(r){-1===n.indexOf(r)&&e[r]!==t[r]&&(e[r]=t[r])})),t&&s(e,t)}function o(e){return(o=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function s(e,t){return(s=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function u(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}function f(e,t,n){return(f=u()?Reflect.construct:function(e,t,n){var r=[null];r.push.apply(r,t);var i=new(Function.bind.apply(e,r));return n&&s(i,n.prototype),i}).apply(null,arguments)}function c(e){var t="function"==typeof Map?new Map:void 0;return(c=function(e){if(null===e||(n=e,-1===Function.toString.call(n).indexOf("[native code]")))return e;var n;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,r)}function r(){return f(e,arguments,o(this).constructor)}return r.prototype=Object.create(e.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),s(r,e)})(e)}function l(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function h(e,t){return!t||"object"!=typeof t&&"function"!=typeof t?l(e):t}function d(e){var t=u();return function(){var n,r=o(e);if(t){var i=o(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return h(this,n)}}function p(e,t,n){return(p="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,t,n){var r=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=o(e)););return e}(e,t);if(r){var i=Object.getOwnPropertyDescriptor(r,t);return i.get?i.get.call(n):i.value}})(e,t,n||e)}var g="undefined"!=typeof self?self:global,v=Object.values||function(e){var t=[];for(var n in e)t.push(e[n]);return t},y=Object.entries||function(e){var t=[];for(var n in e)t.push([n,e[n]]);return t},m=Object.assign||function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return n.forEach((function(t){for(var n in t)e[n]=t[n]})),e},k=Object.fromEntries||function(e){var t={};return S(e).forEach((function(e){var n=e[0],r=e[1];t[n]=r})),t},S=Array.from||function(e){if(e instanceof A){var t=[];return e.forEach((function(e,n){return t.push([n,e])})),t}return Array.prototype.slice.call(e)};function C(e){return-1!==this.indexOf(e)}Array.prototype.includes||(Array.prototype.includes=C),String.prototype.includes||(String.prototype.includes=C),String.prototype.startsWith||(String.prototype.startsWith=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return this.substring(t,t+e.length)===e}),String.prototype.endsWith||(String.prototype.endsWith=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.length;return this.substring(t-e.length,t)===e});var P=function(e){var t=[];if(Object.defineProperties(t,{size:{get:function(){return this.length}},has:{value:function(e){return-1!==this.indexOf(e)}},add:{value:function(e){this.has(e)||this.push(e)}},delete:{value:function(e){if(this.has(e)){var t=this.indexOf(e);this.splice(t,1)}}}}),Array.isArray(e))for(var n=0;n<e.length;n++)t.add(e[n]);return t},b=function(e){return new A(e)},A=void 0!==g.Map&&void 0!==g.Map.prototype.keys?g.Map:function(){function e(n){if(t(this,e),this.clear(),n)for(var r=0;r<n.length;r++)this.set(n[r][0],n[r][1])}return r(e,[{key:"clear",value:function(){this._map={},this._keys=[]}},{key:"size",get:function(){return this._keys.length}},{key:"get",value:function(e){return this._map["map_"+e]}},{key:"set",value:function(e,t){return this._map["map_"+e]=t,this._keys.indexOf(e)<0&&this._keys.push(e),this}},{key:"has",value:function(e){return this._keys.indexOf(e)>=0}},{key:"delete",value:function(e){var t=this._keys.indexOf(e);return!(t<0)&&(delete this._map["map_"+e],this._keys.splice(t,1),!0)}},{key:"keys",value:function(){return this._keys.slice(0)}},{key:"values",value:function(){var e=this;return this._keys.map((function(t){return e.get(t)}))}},{key:"entries",value:function(){var e=this;return this._keys.map((function(t){return[t,e.get(t)]}))}},{key:"forEach",value:function(e,t){for(var n=0;n<this._keys.length;n++)e.call(t,this._map["map_"+this._keys[n]],this._keys[n],this)}}]),e}(),I=g.fetch;g.fetch||(I=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new Promise((function(n,r){var i=new XMLHttpRequest;if(i.open("get",e,!0),i.responseType="arraybuffer",i.onerror=r,t.headers)for(var a in t.headers)i.setRequestHeader(a,t.headers[a]);i.onload=function(){n({status:i.status,arrayBuffer:function(){return Promise.resolve(i.response)}})},i.send(null)}))});var T="undefined"!=typeof navigator,O=T&&"undefined"==typeof HTMLImageElement,D=!("undefined"==typeof global||"undefined"==typeof process||!process.versions||!process.versions.node),w=g.Buffer,x=g.BigInt,R=!!w,M=function(e){return U(e)?void 0:e},L=function(e){return void 0!==e};function U(e){return void 0===e||(e instanceof A?0===e.size:0===v(e).filter(L).length)}function F(e){var t=new Error(e);throw delete t.stack,t}function E(e){return""===(e=function(e){for(;e.endsWith("\0");)e=e.slice(0,-1);return e}(e).trim())?void 0:e}function B(e){var t=function(e){var t=0;return e.ifd0.enabled&&(t+=1024),e.exif.enabled&&(t+=2048),e.makerNote&&(t+=2048),e.userComment&&(t+=1024),e.gps.enabled&&(t+=512),e.interop.enabled&&(t+=100),e.ifd1.enabled&&(t+=1024),t+2048}(e);return e.jfif.enabled&&(t+=50),e.xmp.enabled&&(t+=2e4),e.iptc.enabled&&(t+=14e3),e.icc.enabled&&(t+=6e3),t}var N=function(e){return String.fromCharCode.apply(null,e)},G="undefined"!=typeof TextDecoder?new TextDecoder("utf-8"):void 0;function j(e){return G?G.decode(e):R?Buffer.from(e).toString("utf8"):decodeURIComponent(escape(N(e)))}var V=function(){function e(n){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=arguments.length>2?arguments[2]:void 0,a=arguments.length>3?arguments[3]:void 0;if(t(this,e),"boolean"==typeof a&&(this.le=a),Array.isArray(n)&&(n=new Uint8Array(n)),0===n)this.byteOffset=0,this.byteLength=0;else if(n instanceof ArrayBuffer){void 0===i&&(i=n.byteLength-r);var o=new DataView(n,r,i);this._swapDataView(o)}else if(n instanceof Uint8Array||n instanceof DataView||n instanceof e){void 0===i&&(i=n.byteLength-r),(r+=n.byteOffset)+i>n.byteOffset+n.byteLength&&F("Creating view outside of available memory in ArrayBuffer");var s=new DataView(n.buffer,r,i);this._swapDataView(s)}else if("number"==typeof n){var u=new DataView(new ArrayBuffer(n));this._swapDataView(u)}else F("Invalid input argument for BufferView: "+n)}return r(e,[{key:"_swapArrayBuffer",value:function(e){this._swapDataView(new DataView(e))}},{key:"_swapBuffer",value:function(e){this._swapDataView(new DataView(e.buffer,e.byteOffset,e.byteLength))}},{key:"_swapDataView",value:function(e){this.dataView=e,this.buffer=e.buffer,this.byteOffset=e.byteOffset,this.byteLength=e.byteLength}},{key:"_lengthToEnd",value:function(e){return this.byteLength-e}},{key:"set",value:function(t,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e;t instanceof DataView||t instanceof e?t=new Uint8Array(t.buffer,t.byteOffset,t.byteLength):t instanceof ArrayBuffer&&(t=new Uint8Array(t)),t instanceof Uint8Array||F("BufferView.set(): Invalid data argument.");var i=this.toUint8();return i.set(t,n),new r(this,n,t.byteLength)}},{key:"subarray",value:function(t,n){return new e(this,t,n=n||this._lengthToEnd(t))}},{key:"toUint8",value:function(){return new Uint8Array(this.buffer,this.byteOffset,this.byteLength)}},{key:"getUint8Array",value:function(e,t){return new Uint8Array(this.buffer,this.byteOffset+e,t)}},{key:"getString",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.byteLength,n=this.getUint8Array(e,t);return j(n)}},{key:"getLatin1String",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.byteLength,n=this.getUint8Array(e,t);return N(n)}},{key:"getUnicodeString",value:function(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.byteLength,n=[],r=0;r<t&&e+r<this.byteLength;r+=2)n.push(this.getUint16(e+r));return N(n)}},{key:"getInt8",value:function(e){return this.dataView.getInt8(e)}},{key:"getUint8",value:function(e){return this.dataView.getUint8(e)}},{key:"getInt16",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.le;return this.dataView.getInt16(e,t)}},{key:"getInt32",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.le;return this.dataView.getInt32(e,t)}},{key:"getUint16",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.le;return this.dataView.getUint16(e,t)}},{key:"getUint32",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.le;return this.dataView.getUint32(e,t)}},{key:"getFloat32",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.le;return this.dataView.getFloat32(e,t)}},{key:"getFloat64",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.le;return this.dataView.getFloat64(e,t)}},{key:"getFloat",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.le;return this.dataView.getFloat32(e,t)}},{key:"getDouble",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.le;return this.dataView.getFloat64(e,t)}},{key:"getUintBytes",value:function(e,t,n){switch(t){case 1:return this.getUint8(e,n);case 2:return this.getUint16(e,n);case 4:return this.getUint32(e,n);case 8:return this.getUint64&&this.getUint64(e,n)}}},{key:"getUint",value:function(e,t,n){switch(t){case 8:return this.getUint8(e,n);case 16:return this.getUint16(e,n);case 32:return this.getUint32(e,n);case 64:return this.getUint64&&this.getUint64(e,n)}}},{key:"toString",value:function(e){return this.dataView.toString(e,this.constructor.name)}},{key:"ensureChunk",value:function(){}}],[{key:"from",value:function(t,n){return t instanceof this&&t.le===n?t:new e(t,void 0,void 0,n)}}]),e}();function z(e,t){F("".concat(e," '").concat(t,"' was not loaded, try using full build of exifr."))}var H=function(e){a(i,e);var n=d(i);function i(e){var r;return t(this,i),(r=n.call(this)).kind=e,r}return r(i,[{key:"get",value:function(e,t){return this.has(e)||z(this.kind,e),t&&(e in t||function(e,t){F("Unknown ".concat(e," '").concat(t,"'."))}(this.kind,e),t[e].enabled||z(this.kind,e)),p(o(i.prototype),"get",this).call(this,e)}},{key:"keyList",value:function(){return S(this.keys())}}]),i}(c(A)),_=new H("file parser"),W=new H("segment parser"),K=new H("file reader");function X(e,t,n){return n?t?t(e):e:(e&&e.then||(e=Promise.resolve(e)),t?e.then(t):e)}var Y=J((function(e,t){return X(t(e),(function(e){return new V(e)}))}));function J(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];try{return Promise.resolve(e.apply(this,t))}catch(e){return Promise.reject(e)}}}var q=J((function(e,t,n){var r=new(K.get(n))(e,t);return X(r.read(),(function(){return r}))})),Q=J((function(e,t,n,r){return K.has(n)?q(e,t,n):r?Y(e,r):(F("Parser ".concat(n," is not loaded")),X())})),Z="Invalid input argument";function $(e,t){return"string"==typeof e?ee(e,t):T&&!O&&e instanceof HTMLImageElement?ee(e.src,t):e instanceof Uint8Array||e instanceof ArrayBuffer||e instanceof DataView?new V(e):T&&e instanceof Blob?Q(e,t,"blob",ne):void F(Z)}function ee(e,t){return(n=e).startsWith("data:")||n.length>1e4?q(e,t,"base64"):D&&e.includes("://")?Q(e,t,"url",te):D?q(e,t,"fs"):T?Q(e,t,"url",te):void F(Z);var n}var te=function(e){return I(e).then((function(e){return e.arrayBuffer()}))},ne=function(e){return new Promise((function(t,n){var r=new FileReader;r.onloadend=function(){return t(r.result||new ArrayBuffer)},r.onerror=n,r.readAsArrayBuffer(e)}))};var re=function(e){a(i,e);var n=d(i);function i(){return t(this,i),n.apply(this,arguments)}return r(i,[{key:"tagKeys",get:function(){return this.allKeys||(this.allKeys=S(this.keys())),this.allKeys}},{key:"tagValues",get:function(){return this.allValues||(this.allValues=S(this.values())),this.allValues}}]),i}(c(A));function ie(e,t,n){var r=new re,i=n;Array.isArray(i)||("function"==typeof i.entries&&(i=i.entries()),i=S(i));for(var a=0;a<i.length;a++){var o=i[a],s=o[0],u=o[1];r.set(s,u)}if(Array.isArray(t)){var f=t;Array.isArray(f)||("function"==typeof f.entries&&(f=f.entries()),f=S(f));for(var c=0;c<f.length;c++){var l=f[c];e.set(l,r)}}else e.set(t,r);return r}function ae(e,t,n){var r,i=e.get(t),a=n;Array.isArray(a)||("function"==typeof a.entries&&(a=a.entries()),a=S(a));for(var o=0;o<a.length;o++)r=a[o],i.set(r[0],r[1])}var oe=b(),se=b(),ue=b(),fe=37500,ce=37510,le=700,he=33723,de=34675,pe=34665,ge=34853,ve=40965,ye=["chunked","firstChunkSize","firstChunkSizeNode","firstChunkSizeBrowser","chunkSize","chunkLimit"],me=["jfif","xmp","icc","iptc","ihdr"],ke=["tiff"].concat(me),Se=["ifd0","ifd1","exif","gps","interop"],Ce=[].concat(ke,Se),Pe=["makerNote","userComment"],be=["translateKeys","translateValues","reviveValues","multiSegment"],Ae=[].concat(be,["sanitize","mergeOutput","silentErrors"]),Ie=function(){function e(){t(this,e)}return r(e,[{key:"translate",get:function(){return this.translateKeys||this.translateValues||this.reviveValues}}]),e}(),Te=function(e){a(o,e);var n=d(o);function o(e,r,a,s){var u;if(t(this,o),i(l(u=n.call(this)),"enabled",!1),i(l(u),"skip",P()),i(l(u),"pick",P()),i(l(u),"deps",P()),i(l(u),"translateKeys",!1),i(l(u),"translateValues",!1),i(l(u),"reviveValues",!1),u.key=e,u.enabled=r,u.parse=u.enabled,u.applyInheritables(s),u.canBeFiltered=Se.includes(e),u.canBeFiltered&&(u.dict=oe.get(e)),void 0!==a)if(Array.isArray(a))u.parse=u.enabled=!0,u.canBeFiltered&&a.length>0&&u.translateTagSet(a,u.pick);else if("object"==typeof a){if(u.enabled=!0,u.parse=!1!==a.parse,u.canBeFiltered){var f=a.pick,c=a.skip;f&&f.length>0&&u.translateTagSet(f,u.pick),c&&c.length>0&&u.translateTagSet(c,u.skip)}u.applyInheritables(a)}else!0===a||!1===a?u.parse=u.enabled=a:F("Invalid options argument: ".concat(a));return u}return r(o,[{key:"needed",get:function(){return this.enabled||this.deps.size>0}},{key:"applyInheritables",value:function(e){var t,n,r=be;Array.isArray(r)||("function"==typeof r.entries&&(r=r.entries()),r=S(r));for(var i=0;i<r.length;i++)void 0!==(n=e[t=r[i]])&&(this[t]=n)}},{key:"translateTagSet",value:function(e,t){if(this.dict){var n,r,i=this.dict,a=i.tagKeys,o=i.tagValues,s=e;Array.isArray(s)||("function"==typeof s.entries&&(s=s.entries()),s=S(s));for(var u=0;u<s.length;u++)"string"==typeof(n=s[u])?(-1===(r=o.indexOf(n))&&(r=a.indexOf(Number(n))),-1!==r&&t.add(Number(a[r]))):t.add(n)}else{var f=e;Array.isArray(f)||("function"==typeof f.entries&&(f=f.entries()),f=S(f));for(var c=0;c<f.length;c++){var l=f[c];t.add(l)}}}},{key:"finalizeFilters",value:function(){!this.enabled&&this.deps.size>0?(this.enabled=!0,Me(this.pick,this.deps)):this.enabled&&this.pick.size>0&&Me(this.pick,this.deps)}}]),o}(Ie),Oe={jfif:!1,tiff:!0,xmp:!1,icc:!1,iptc:!1,ifd0:!0,ifd1:!1,exif:!0,gps:!0,interop:!1,ihdr:void 0,makerNote:!1,userComment:!1,multiSegment:!1,skip:[],pick:[],translateKeys:!0,translateValues:!0,reviveValues:!0,sanitize:!0,mergeOutput:!0,silentErrors:!0,chunked:!0,firstChunkSize:void 0,firstChunkSizeNode:512,firstChunkSizeBrowser:65536,chunkSize:65536,chunkLimit:5},De=b(),we=function(e){a(i,e);var n=d(i);function i(e){var r;return t(this,i),r=n.call(this),!0===e?r.setupFromTrue():void 0===e?r.setupFromUndefined():Array.isArray(e)?r.setupFromArray(e):"object"==typeof e?r.setupFromObject(e):F("Invalid options argument ".concat(e)),void 0===r.firstChunkSize&&(r.firstChunkSize=T?r.firstChunkSizeBrowser:r.firstChunkSizeNode),r.mergeOutput&&(r.ifd1.enabled=!1),r.filterNestedSegmentTags(),r.traverseTiffDependencyTree(),r.checkLoadedPlugins(),r}return r(i,[{key:"setupFromUndefined",value:function(){var e,t=ye;Array.isArray(t)||("function"==typeof t.entries&&(t=t.entries()),t=S(t));for(var n=0;n<t.length;n++)this[e=t[n]]=Oe[e];var r=Ae;Array.isArray(r)||("function"==typeof r.entries&&(r=r.entries()),r=S(r));for(var i=0;i<r.length;i++)this[e=r[i]]=Oe[e];var a=Pe;Array.isArray(a)||("function"==typeof a.entries&&(a=a.entries()),a=S(a));for(var o=0;o<a.length;o++)this[e=a[o]]=Oe[e];var s=Ce;Array.isArray(s)||("function"==typeof s.entries&&(s=s.entries()),s=S(s));for(var u=0;u<s.length;u++)this[e=s[u]]=new Te(e,Oe[e],void 0,this)}},{key:"setupFromTrue",value:function(){var e,t=ye;Array.isArray(t)||("function"==typeof t.entries&&(t=t.entries()),t=S(t));for(var n=0;n<t.length;n++)this[e=t[n]]=Oe[e];var r=Ae;Array.isArray(r)||("function"==typeof r.entries&&(r=r.entries()),r=S(r));for(var i=0;i<r.length;i++)this[e=r[i]]=Oe[e];var a=Pe;Array.isArray(a)||("function"==typeof a.entries&&(a=a.entries()),a=S(a));for(var o=0;o<a.length;o++)this[e=a[o]]=!0;var s=Ce;Array.isArray(s)||("function"==typeof s.entries&&(s=s.entries()),s=S(s));for(var u=0;u<s.length;u++)this[e=s[u]]=new Te(e,!0,void 0,this)}},{key:"setupFromArray",value:function(e){var t,n=ye;Array.isArray(n)||("function"==typeof n.entries&&(n=n.entries()),n=S(n));for(var r=0;r<n.length;r++)this[t=n[r]]=Oe[t];var i=Ae;Array.isArray(i)||("function"==typeof i.entries&&(i=i.entries()),i=S(i));for(var a=0;a<i.length;a++)this[t=i[a]]=Oe[t];var o=Pe;Array.isArray(o)||("function"==typeof o.entries&&(o=o.entries()),o=S(o));for(var s=0;s<o.length;s++)this[t=o[s]]=Oe[t];var u=Ce;Array.isArray(u)||("function"==typeof u.entries&&(u=u.entries()),u=S(u));for(var f=0;f<u.length;f++)this[t=u[f]]=new Te(t,!1,void 0,this);this.setupGlobalFilters(e,void 0,Se)}},{key:"setupFromObject",value:function(e){var t;Se.ifd0=Se.ifd0||Se.image,Se.ifd1=Se.ifd1||Se.thumbnail,m(this,e);var n=ye;Array.isArray(n)||("function"==typeof n.entries&&(n=n.entries()),n=S(n));for(var r=0;r<n.length;r++)this[t=n[r]]=Re(e[t],Oe[t]);var i=Ae;Array.isArray(i)||("function"==typeof i.entries&&(i=i.entries()),i=S(i));for(var a=0;a<i.length;a++)this[t=i[a]]=Re(e[t],Oe[t]);var o=Pe;Array.isArray(o)||("function"==typeof o.entries&&(o=o.entries()),o=S(o));for(var s=0;s<o.length;s++)this[t=o[s]]=Re(e[t],Oe[t]);var u=ke;Array.isArray(u)||("function"==typeof u.entries&&(u=u.entries()),u=S(u));for(var f=0;f<u.length;f++)this[t=u[f]]=new Te(t,Oe[t],e[t],this);var c=Se;Array.isArray(c)||("function"==typeof c.entries&&(c=c.entries()),c=S(c));for(var l=0;l<c.length;l++)this[t=c[l]]=new Te(t,Oe[t],e[t],this.tiff);this.setupGlobalFilters(e.pick,e.skip,Se,Ce),!0===e.tiff?this.batchEnableWithBool(Se,!0):!1===e.tiff?this.batchEnableWithUserValue(Se,e):Array.isArray(e.tiff)?this.setupGlobalFilters(e.tiff,void 0,Se):"object"==typeof e.tiff&&this.setupGlobalFilters(e.tiff.pick,e.tiff.skip,Se)}},{key:"batchEnableWithBool",value:function(e,t){var n=e;Array.isArray(n)||("function"==typeof n.entries&&(n=n.entries()),n=S(n));for(var r=0;r<n.length;r++){this[n[r]].enabled=t}}},{key:"batchEnableWithUserValue",value:function(e,t){var n=e;Array.isArray(n)||("function"==typeof n.entries&&(n=n.entries()),n=S(n));for(var r=0;r<n.length;r++){var i=n[r],a=t[i];this[i].enabled=!1!==a&&void 0!==a}}},{key:"setupGlobalFilters",value:function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:n;if(e&&e.length){var i=r;Array.isArray(i)||("function"==typeof i.entries&&(i=i.entries()),i=S(i));for(var a=0;a<i.length;a++){var o=i[a];this[o].enabled=!1}var s=xe(e,n),u=s;Array.isArray(u)||("function"==typeof u.entries&&(u=u.entries()),u=S(u));for(var f=0;f<u.length;f++){var c=u[f],l=c[0],h=c[1];Me(this[l].pick,h),this[l].enabled=!0}}else if(t&&t.length){var d=xe(t,n),p=d;Array.isArray(p)||("function"==typeof p.entries&&(p=p.entries()),p=S(p));for(var g=0;g<p.length;g++){var v=p[g],y=v[0],m=v[1];Me(this[y].skip,m)}}}},{key:"filterNestedSegmentTags",value:function(){var e=this.ifd0,t=this.exif,n=this.xmp,r=this.iptc,i=this.icc;this.makerNote?t.deps.add(fe):t.skip.add(fe),this.userComment?t.deps.add(ce):t.skip.add(ce),n.enabled||e.skip.add(le),r.enabled||e.skip.add(he),i.enabled||e.skip.add(de)}},{key:"traverseTiffDependencyTree",value:function(){var e=this,t=this.ifd0,n=this.exif,r=this.gps;this.interop.needed&&(n.deps.add(ve),t.deps.add(ve)),n.needed&&t.deps.add(pe),r.needed&&t.deps.add(ge),this.tiff.enabled=Se.some((function(t){return!0===e[t].enabled}))||this.makerNote||this.userComment;var i=Se;Array.isArray(i)||("function"==typeof i.entries&&(i=i.entries()),i=S(i));for(var a=0;a<i.length;a++){this[i[a]].finalizeFilters()}}},{key:"onlyTiff",get:function(){var e=this;return!me.map((function(t){return e[t].enabled})).some((function(e){return!0===e}))&&this.tiff.enabled}},{key:"checkLoadedPlugins",value:function(){var e=ke;Array.isArray(e)||("function"==typeof e.entries&&(e=e.entries()),e=S(e));for(var t=0;t<e.length;t++){var n=e[t];this[n].enabled&&!W.has(n)&&z("segment parser",n)}}}],[{key:"useCached",value:function(e){var t=De.get(e);return void 0!==t||(t=new this(e),De.set(e,t)),t}}]),i}(Ie);function xe(e,t){var n,r,i,a=[],o=t;Array.isArray(o)||("function"==typeof o.entries&&(o=o.entries()),o=S(o));for(var s=0;s<o.length;s++){r=o[s],n=[];var u=oe.get(r);Array.isArray(u)||("function"==typeof u.entries&&(u=u.entries()),u=S(u));for(var f=0;f<u.length;f++)i=u[f],(e.includes(i[0])||e.includes(i[1]))&&n.push(i[0]);n.length&&a.push([r,n])}return a}function Re(e,t){return void 0!==e?e:void 0!==t?t:void 0}function Me(e,t){var n=t;Array.isArray(n)||("function"==typeof n.entries&&(n=n.entries()),n=S(n));for(var r=0;r<n.length;r++){var i=n[r];e.add(i)}}function Le(e,t,n){return n?t?t(e):e:(e&&e.then||(e=Promise.resolve(e)),t?e.then(t):e)}function Ue(){}function Fe(e,t){if(!t)return e&&e.then?e.then(Ue):Promise.resolve()}function Ee(e,t){var n=e();return n&&n.then?n.then(t):t(n)}i(we,"default",Oe);var Be=function(){function e(n){var r=this;t(this,e),i(this,"parsers",{}),i(this,"output",{}),i(this,"errors",[]),i(this,"pushToErrors",(function(e){return r.errors.push(e)})),this.options=we.useCached(n)}return r(e,[{key:"read",value:function(e){try{var t=this;return Le($(e,t.options),(function(e){t.file=e}))}catch(e){return Promise.reject(e)}}},{key:"setup",value:function(){if(!this.fileParser){var e=this.file,t=e.getUint16(0),n=_;Array.isArray(n)||("function"==typeof n.entries&&(n=n.entries()),n=S(n));for(var r=0;r<n.length;r++){var i=n[r],a=i[0],o=i[1];if(o.canHandle(e,t))return this.fileParser=new o(this.options,this.file,this.parsers),e[a]=!0}this.file.close&&this.file.close(),F("Unknown file format")}}},{key:"parse",value:function(){try{var e=this,t=e.output,n=e.errors;return e.setup(),Ee((function(){return e.options.silentErrors?Le(e.executeParsers().catch(e.pushToErrors),(function(){n.push.apply(n,e.fileParser.errors)})):Fe(e.executeParsers())}),(function(){return e.file.close&&e.file.close(),e.options.silentErrors&&n.length>0&&(t.errors=n),M(t)}))}catch(e){return Promise.reject(e)}}},{key:"executeParsers",value:function(){try{var e=this,t=e.output;return Le(e.fileParser.parse(),(function(){var n=v(e.parsers).map(function(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];try{return Promise.resolve(e.apply(this,t))}catch(e){return Promise.reject(e)}}}((function(e){return Le(e.parse(),(function(n){e.assignToOutput(t,n)}))})));return e.options.silentErrors&&(n=n.map((function(t){return t.catch(e.pushToErrors)}))),Fe(Promise.all(n))}))}catch(e){return Promise.reject(e)}}},{key:"extractThumbnail",value:function(){try{var e=this;e.setup();var t,n=e.options,r=e.file,i=W.get("tiff",n);return Ee((function(){if(!r.tiff)return function(e){var t=e();if(t&&t.then)return t.then(Ue)}((function(){if(r.jpeg)return Le(e.fileParser.getOrFindSegment("tiff"),(function(e){t=e}))}));t={start:0,type:"tiff"}}),(function(){if(void 0!==t)return Le(e.fileParser.ensureSegmentChunk(t),(function(t){return Le((e.parsers.tiff=new i(t,n,r)).extractThumbnail(),(function(e){return r.close&&r.close(),e}))}))}))}catch(e){return Promise.reject(e)}}}]),e}();var Ne=function(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];try{return Promise.resolve(e.apply(this,t))}catch(e){return Promise.reject(e)}}}((function(e,t){var n,r,i,a=new Be(t);return n=a.read(e),r=function(){return a.parse()},i?r?r(n):n:(n&&n.then||(n=Promise.resolve(n)),r?n.then(r):n)})),Ge=Object.freeze({__proto__:null,parse:Ne,Exifr:Be,fileParsers:_,segmentParsers:W,fileReaders:K,tagKeys:oe,tagValues:se,tagRevivers:ue,createDictionary:ie,extendDictionary:ae,fetchUrlAsArrayBuffer:te,readBlobAsArrayBuffer:ne,chunkedProps:ye,otherSegments:me,segments:ke,tiffBlocks:Se,segmentsAndBlocks:Ce,tiffExtractables:Pe,inheritables:be,allFormatters:Ae,Options:we});function je(){}var Ve=function(){function e(n,r,a){var o=this;t(this,e),i(this,"errors",[]),i(this,"ensureSegmentChunk",function(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];try{return Promise.resolve(e.apply(this,t))}catch(e){return Promise.reject(e)}}}((function(e){var t,n,r,i=e.start,a=e.size||65536;return t=function(){if(o.file.chunked)return function(e){var t=e();if(t&&t.then)return t.then(je)}((function(){if(!o.file.available(i,a))return function(e){if(e&&e.then)return e.then(je)}(function(e,t){try{var n=e()}catch(e){return t(e)}return n&&n.then?n.then(void 0,t):n}((function(){return t=o.file.readChunk(i,a),n=function(t){e.chunk=t},r?n?n(t):t:(t&&t.then||(t=Promise.resolve(t)),n?t.then(n):t);var t,n,r}),(function(t){F("Couldn't read segment: ".concat(JSON.stringify(e),". ").concat(t.message))})));e.chunk=o.file.subarray(i,a)}));o.file.byteLength>i+a?e.chunk=o.file.subarray(i,a):void 0===e.size?e.chunk=o.file.subarray(i):F("Segment unreachable: "+JSON.stringify(e))},n=function(){return e.chunk},(r=t())&&r.then?r.then(n):n(r)}))),this.extendOptions&&this.extendOptions(n),this.options=n,this.file=r,this.parsers=a}return r(e,[{key:"injectSegment",value:function(e,t){this.options[e].enabled&&this.createParser(e,t)}},{key:"createParser",value:function(e,t){var n=new(W.get(e))(t,this.options,this.file);return this.parsers[e]=n}},{key:"createParsers",value:function(e){var t=e;Array.isArray(t)||("function"==typeof t.entries&&(t=t.entries()),t=S(t));for(var n=0;n<t.length;n++){var r=t[n],i=r.type,a=r.chunk,o=this.options[i];if(o&&o.enabled){var s=this.parsers[i];s&&s.append||s||this.createParser(i,a)}}}},{key:"readSegments",value:function(e){try{var t=e.map(this.ensureSegmentChunk);return function(e,t){if(!t)return e&&e.then?e.then(je):Promise.resolve()}(Promise.all(t))}catch(e){return Promise.reject(e)}}}]),e}(),ze=function(){function e(n){var r=this,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=arguments.length>2?arguments[2]:void 0;t(this,e),i(this,"errors",[]),i(this,"raw",b()),i(this,"handleError",(function(e){if(!r.options.silentErrors)throw e;r.errors.push(e.message)})),this.chunk=this.normalizeInput(n),this.file=o,this.type=this.constructor.type,this.globalOptions=this.options=a,this.localOptions=a[this.type],this.canTranslate=this.localOptions&&this.localOptions.translate}return r(e,[{key:"normalizeInput",value:function(e){return e instanceof V?e:new V(e)}},{key:"translate",value:function(){this.canTranslate&&(this.translated=this.translateBlock(this.raw,this.type))}},{key:"output",get:function(){return this.translated?this.translated:this.raw?k(this.raw):void 0}},{key:"translateBlock",value:function(e,t){var n=ue.get(t),r=se.get(t),i=oe.get(t),a=this.options[t],o=a.reviveValues&&!!n,s=a.translateValues&&!!r,u=a.translateKeys&&!!i,f={},c=e;Array.isArray(c)||("function"==typeof c.entries&&(c=c.entries()),c=S(c));for(var l=0;l<c.length;l++){var h=c[l],d=h[0],p=h[1];o&&n.has(d)?p=n.get(d)(p):s&&r.has(d)&&(p=this.translateValue(p,r.get(d))),u&&i.has(d)&&(d=i.get(d)||d),f[d]=p}return f}},{key:"translateValue",value:function(e,t){return t[e]||t.DEFAULT||e}},{key:"assignToOutput",value:function(e,t){this.assignObjectToOutput(e,this.constructor.type,t)}},{key:"assignObjectToOutput",value:function(e,t,n){if(this.globalOptions.mergeOutput)return m(e,n);e[t]?m(e[t],n):e[t]=n}}],[{key:"findPosition",value:function(e,t){var n=e.getUint16(t+2)+2,r="function"==typeof this.headerLength?this.headerLength(e,t,n):this.headerLength,i=t+r,a=n-r;return{offset:t,length:n,headerLength:r,start:i,size:a,end:i+a}}},{key:"parse",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=new we(i({},this.type,t)),r=new this(e,n,e);return r.parse()}}]),e}();function He(e,t,n){return n?t?t(e):e:(e&&e.then||(e=Promise.resolve(e)),t?e.then(t):e)}i(ze,"headerLength",4),i(ze,"type",void 0),i(ze,"multiSegment",!1),i(ze,"canHandle",(function(){return!1}));function _e(){}function We(e,t){if(!t)return e&&e.then?e.then(_e):Promise.resolve()}function Ke(e){var t=e();if(t&&t.then)return t.then(_e)}function Xe(e,t){var n=e();return n&&n.then?n.then(t):t(n)}function Ye(e,t,n){if(!e.s){if(n instanceof Je){if(!n.s)return void(n.o=Ye.bind(null,e,t));1&t&&(t=n.s),n=n.v}if(n&&n.then)return void n.then(Ye.bind(null,e,t),Ye.bind(null,e,2));e.s=t,e.v=n;var r=e.o;r&&r(e)}}var Je=function(){function e(){}return e.prototype.then=function(t,n){var r=new e,i=this.s;if(i){var a=1&i?t:n;if(a){try{Ye(r,1,a(this.v))}catch(e){Ye(r,2,e)}return r}return this}return this.o=function(e){try{var i=e.v;1&e.s?Ye(r,1,t?t(i):i):n?Ye(r,1,n(i)):Ye(r,2,i)}catch(e){Ye(r,2,e)}},r},e}();function qe(e){return e instanceof Je&&1&e.s}function Qe(e,t,n){for(var r;;){var i=e();if(qe(i)&&(i=i.v),!i)return a;if(i.then){r=0;break}var a=n();if(a&&a.then){if(!qe(a)){r=1;break}a=a.s}if(t){var o=t();if(o&&o.then&&!qe(o)){r=2;break}}}var s=new Je,u=Ye.bind(null,s,2);return(0===r?i.then(c):1===r?a.then(f):o.then(l)).then(void 0,u),s;function f(r){a=r;do{if(t&&(o=t())&&o.then&&!qe(o))return void o.then(l).then(void 0,u);if(!(i=e())||qe(i)&&!i.v)return void Ye(s,1,a);if(i.then)return void i.then(c).then(void 0,u);qe(a=n())&&(a=a.v)}while(!a||!a.then);a.then(f).then(void 0,u)}function c(e){e?(a=n())&&a.then?a.then(f).then(void 0,u):f(a):Ye(s,1,a)}function l(){(i=e())?i.then?i.then(c).then(void 0,u):c(i):Ye(s,1,a)}}function Ze(e){return 192===e||194===e||196===e||219===e||221===e||218===e||254===e}function $e(e){return e>=224&&e<=239}function et(e,t,n){var r=W;Array.isArray(r)||("function"==typeof r.entries&&(r=r.entries()),r=S(r));for(var i=0;i<r.length;i++){var a=r[i],o=a[0];if(a[1].canHandle(e,t,n))return o}}var tt=function(e){a(o,e);var n=d(o);function o(){var e;t(this,o);for(var r=arguments.length,a=new Array(r),s=0;s<r;s++)a[s]=arguments[s];return i(l(e=n.call.apply(n,[this].concat(a))),"appSegments",[]),i(l(e),"jpegSegments",[]),i(l(e),"unknownSegments",[]),e}return r(o,[{key:"parse",value:function(){try{var e=this;return He(e.findAppSegments(),(function(){return He(e.readSegments(e.appSegments),(function(){e.mergeMultiSegments(),e.createParsers(e.mergedAppSegments||e.appSegments)}))}))}catch(e){return Promise.reject(e)}}},{key:"setupSegmentFinderArgs",value:function(e){var t=this;!0===e?(this.findAll=!0,this.wanted=P(W.keyList())):(e=void 0===e?W.keyList().filter((function(e){return t.options[e].enabled})):e.filter((function(e){return t.options[e].enabled&&W.has(e)})),this.findAll=!1,this.remaining=P(e),this.wanted=P(e)),this.unfinishedMultiSegment=!1}},{key:"findAppSegments",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1?arguments[1]:void 0;try{var n=this;n.setupSegmentFinderArgs(t);var r=n.file,i=n.findAll,a=n.wanted,o=n.remaining;return Xe((function(){if(!i&&n.file.chunked)return i=S(a).some((function(e){var t=W.get(e),r=n.options[e];return t.multiSegment&&r.multiSegment})),Ke((function(){if(i)return We(n.file.readWhole())}))}),(function(){var t=!1;if(e=n.findAppSegmentsInRange(e,r.byteLength),!n.options.onlyTiff)return function(){if(r.chunked){var i=!1;return Qe((function(){return!t&&o.size>0&&!i&&(!!r.canReadNextChunk||!!n.unfinishedMultiSegment)}),void 0,(function(){var a=r.nextChunkOffset,o=n.appSegments.some((function(e){return!n.file.available(e.offset||e.start,e.length||e.size)}));return Xe((function(){return e>a&&!o?He(r.readNextChunk(e),(function(e){i=!e})):He(r.readNextChunk(a),(function(e){i=!e}))}),(function(){void 0===(e=n.findAppSegmentsInRange(e,r.byteLength))&&(t=!0)}))}))}}()}))}catch(e){return Promise.reject(e)}}},{key:"findAppSegmentsInRange",value:function(e,t){t-=2;for(var n,r,i,a,o,s,u=this.file,f=this.findAll,c=this.wanted,l=this.remaining,h=this.options;e<t;e++)if(255===u.getUint8(e))if($e(n=u.getUint8(e+1))){if(r=u.getUint16(e+2),(i=et(u,e,r))&&c.has(i)&&(o=(a=W.get(i)).findPosition(u,e),s=h[i],o.type=i,this.appSegments.push(o),!f&&(a.multiSegment&&s.multiSegment?(this.unfinishedMultiSegment=o.chunkNumber<o.chunkCount,this.unfinishedMultiSegment||l.delete(i)):l.delete(i),0===l.size)))break;h.recordUnknownSegments&&((o=ze.findPosition(u,e)).marker=n,this.unknownSegments.push(o)),e+=r+1}else if(Ze(n)){if(r=u.getUint16(e+2),218===n&&!1!==h.stopAfterSos)return;h.recordJpegSegments&&this.jpegSegments.push({offset:e,length:r,marker:n}),e+=r+1}return e}},{key:"mergeMultiSegments",value:function(){var e=this;if(this.appSegments.some((function(e){return e.multiSegment}))){var t=function(e,t){for(var n,r,i,a=b(),o=0;o<e.length;o++)r=(n=e[o])[t],a.has(r)?i=a.get(r):a.set(r,i=[]),i.push(n);return S(a)}(this.appSegments,"type");this.mergedAppSegments=t.map((function(t){var n=t[0],r=t[1],i=W.get(n,e.options);return i.handleMultiSegments?{type:n,chunk:i.handleMultiSegments(r)}:r[0]}))}}},{key:"getSegment",value:function(e){return this.appSegments.find((function(t){return t.type===e}))}},{key:"getOrFindSegment",value:function(e){try{var t=this,n=t.getSegment(e);return Xe((function(){if(void 0===n)return He(t.findAppSegments(0,[e]),(function(){n=t.getSegment(e)}))}),(function(){return n}))}catch(e){return Promise.reject(e)}}}],[{key:"canHandle",value:function(e,t){return 65496===t}}]),o}(Ve);function nt(){}i(tt,"type","jpeg"),_.set("jpeg",tt);function rt(e,t){if(!t)return e&&e.then?e.then(nt):Promise.resolve()}function it(e,t){var n=e();return n&&n.then?n.then(t):t(n)}var at=[void 0,1,1,2,4,8,1,1,2,4,8,4,8,4];var ot=function(e){a(i,e);var n=d(i);function i(){return t(this,i),n.apply(this,arguments)}return r(i,[{key:"parse",value:function(){try{var e=this;e.parseHeader();var t=e.options;return it((function(){if(t.ifd0.enabled)return rt(e.parseIfd0Block())}),(function(){return it((function(){if(t.exif.enabled)return rt(e.safeParse("parseExifBlock"))}),(function(){return it((function(){if(t.gps.enabled)return rt(e.safeParse("parseGpsBlock"))}),(function(){return it((function(){if(t.interop.enabled)return rt(e.safeParse("parseInteropBlock"))}),(function(){return it((function(){if(t.ifd1.enabled)return rt(e.safeParse("parseThumbnailBlock"))}),(function(){return e.createOutput()}))}))}))}))}))}catch(e){return Promise.reject(e)}}},{key:"safeParse",value:function(e){var t=this[e]();return void 0!==t.catch&&(t=t.catch(this.handleError)),t}},{key:"findIfd0Offset",value:function(){void 0===this.ifd0Offset&&(this.ifd0Offset=this.chunk.getUint32(4))}},{key:"findIfd1Offset",value:function(){if(void 0===this.ifd1Offset){this.findIfd0Offset();var e=this.chunk.getUint16(this.ifd0Offset),t=this.ifd0Offset+2+12*e;this.ifd1Offset=this.chunk.getUint32(t)}}},{key:"parseBlock",value:function(e,t){var n=b();return this[t]=n,this.parseTags(e,t,n),n}},{key:"parseIfd0Block",value:function(){try{var e=this;if(e.ifd0)return;var t=e.file;return e.findIfd0Offset(),e.ifd0Offset<8&&F("Malformed EXIF data"),!t.chunked&&e.ifd0Offset>t.byteLength&&F("IFD0 offset points to outside of file.\nthis.ifd0Offset: ".concat(e.ifd0Offset,", file.byteLength: ").concat(t.byteLength)),it((function(){if(t.tiff)return rt(t.ensureChunk(e.ifd0Offset,B(e.options)))}),(function(){var t=e.parseBlock(e.ifd0Offset,"ifd0");if(0!==t.size)return e.exifOffset=t.get(pe),e.interopOffset=t.get(ve),e.gpsOffset=t.get(ge),e.xmp=t.get(le),e.iptc=t.get(he),e.icc=t.get(de),e.options.sanitize&&(t.delete(pe),t.delete(ve),t.delete(ge),t.delete(le),t.delete(he),t.delete(de)),t}))}catch(e){return Promise.reject(e)}}},{key:"parseExifBlock",value:function(){try{var e=this;if(e.exif)return;return it((function(){if(!e.ifd0)return rt(e.parseIfd0Block())}),(function(){if(void 0!==e.exifOffset)return it((function(){if(e.file.tiff)return rt(e.file.ensureChunk(e.exifOffset,B(e.options)))}),(function(){var t=e.parseBlock(e.exifOffset,"exif");return e.interopOffset||(e.interopOffset=t.get(ve)),e.makerNote=t.get(fe),e.userComment=t.get(ce),e.options.sanitize&&(t.delete(ve),t.delete(fe),t.delete(ce)),e.unpack(t,41728),e.unpack(t,41729),t}))}))}catch(e){return Promise.reject(e)}}},{key:"unpack",value:function(e,t){var n=e.get(t);n&&1===n.length&&e.set(t,n[0])}},{key:"parseGpsBlock",value:function(){try{var e=this;if(e.gps)return;return it((function(){if(!e.ifd0)return rt(e.parseIfd0Block())}),(function(){if(void 0!==e.gpsOffset){var t=e.parseBlock(e.gpsOffset,"gps");return t&&t.has(2)&&t.has(4)&&(t.set("latitude",st.apply(void 0,t.get(2).concat([t.get(1)]))),t.set("longitude",st.apply(void 0,t.get(4).concat([t.get(3)])))),t}}))}catch(e){return Promise.reject(e)}}},{key:"parseInteropBlock",value:function(){try{var e=this;if(e.interop)return;return it((function(){if(!e.ifd0)return rt(e.parseIfd0Block())}),(function(){return it((function(){if(void 0===e.interopOffset&&!e.exif)return rt(e.parseExifBlock())}),(function(){if(void 0!==e.interopOffset)return e.parseBlock(e.interopOffset,"interop")}))}))}catch(e){return Promise.reject(e)}}},{key:"parseThumbnailBlock",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{var t=this;if(t.ifd1||t.ifd1Parsed)return;if(t.options.mergeOutput&&!e)return;return t.findIfd1Offset(),t.ifd1Offset>0&&(t.parseBlock(t.ifd1Offset,"ifd1"),t.ifd1Parsed=!0),t.ifd1}catch(e){return Promise.reject(e)}}},{key:"extractThumbnail",value:function(){try{var e=this;return e.headerParsed||e.parseHeader(),it((function(){if(!e.ifd1Parsed)return rt(e.parseThumbnailBlock(!0))}),(function(){if(void 0!==e.ifd1){var t=e.ifd1.get(513),n=e.ifd1.get(514);return e.chunk.getUint8Array(t,n)}}))}catch(e){return Promise.reject(e)}}},{key:"image",get:function(){return this.ifd0}},{key:"thumbnail",get:function(){return this.ifd1}},{key:"createOutput",value:function(){var e,t,n,r={},i=Se;Array.isArray(i)||("function"==typeof i.entries&&(i=i.entries()),i=S(i));for(var a=0;a<i.length;a++)if(!U(e=this[t=i[a]]))if(n=this.canTranslate?this.translateBlock(e,t):k(e),this.options.mergeOutput){if("ifd1"===t)continue;m(r,n)}else r[t]=n;return this.makerNote&&(r.makerNote=this.makerNote),this.userComment&&(r.userComment=this.userComment),r}},{key:"assignToOutput",value:function(e,t){if(this.globalOptions.mergeOutput)m(e,t);else{var n=y(t);Array.isArray(n)||("function"==typeof n.entries&&(n=n.entries()),n=S(n));for(var r=0;r<n.length;r++){var i=n[r],a=i[0],o=i[1];this.assignObjectToOutput(e,a,o)}}}}],[{key:"canHandle",value:function(e,t){return 225===e.getUint8(t+1)&&1165519206===e.getUint32(t+4)&&0===e.getUint16(t+8)}}]),i}(function(e){a(i,e);var n=d(i);function i(){return t(this,i),n.apply(this,arguments)}return r(i,[{key:"parseHeader",value:function(){var e=this.chunk.getUint16();18761===e?this.le=!0:19789===e&&(this.le=!1),this.chunk.le=this.le,this.headerParsed=!0}},{key:"parseTags",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:b(),r=this.options[t],i=r.pick,a=r.skip,o=(i=P(i)).size>0,s=0===a.size,u=this.chunk.getUint16(e);e+=2;for(var f=0;f<u;f++){var c=this.chunk.getUint16(e);if(o){if(i.has(c)&&(n.set(c,this.parseTag(e,c,t)),i.delete(c),0===i.size))break}else!s&&a.has(c)||n.set(c,this.parseTag(e,c,t));e+=12}return n}},{key:"parseTag",value:function(e,t,n){var r=this.chunk,i=r.getUint16(e+2),a=r.getUint32(e+4),o=at[i];if(o*a<=4?e+=8:e=r.getUint32(e+8),(i<1||i>13)&&F("Invalid TIFF value type. block: ".concat(n.toUpperCase(),", tag: ").concat(t.toString(16),", type: ").concat(i,", offset ").concat(e)),e>r.byteLength&&F("Invalid TIFF value offset. block: ".concat(n.toUpperCase(),", tag: ").concat(t.toString(16),", type: ").concat(i,", offset ").concat(e," is outside of chunk size ").concat(r.byteLength)),1===i)return r.getUint8Array(e,a);if(2===i)return E(r.getString(e,a));if(7===i)return r.getUint8Array(e,a);if(1===a)return this.parseTagValue(i,e);for(var s=new(function(e){switch(e){case 1:return Uint8Array;case 3:return Uint16Array;case 4:return Uint32Array;case 5:return Array;case 6:return Int8Array;case 8:return Int16Array;case 9:return Int32Array;case 10:return Array;case 11:return Float32Array;case 12:return Float64Array;default:return Array}}(i))(a),u=o,f=0;f<a;f++)s[f]=this.parseTagValue(i,e),e+=u;return s}},{key:"parseTagValue",value:function(e,t){var n=this.chunk;switch(e){case 1:return n.getUint8(t);case 3:return n.getUint16(t);case 4:return n.getUint32(t);case 5:return n.getUint32(t)/n.getUint32(t+4);case 6:return n.getInt8(t);case 8:return n.getInt16(t);case 9:return n.getInt32(t);case 10:return n.getInt32(t)/n.getInt32(t+4);case 11:return n.getFloat(t);case 12:return n.getDouble(t);case 13:return n.getUint32(t);default:F("Invalid tiff type ".concat(e))}}}]),i}(ze));function st(e,t,n,r){var i=e+t/60+n/3600;return"S"!==r&&"W"!==r||(i*=-1),i}i(ot,"type","tiff"),i(ot,"headerLength",10),W.set("tiff",ot);var ut=Object.freeze({__proto__:null,default:Ge,Exifr:Be,fileParsers:_,segmentParsers:W,fileReaders:K,tagKeys:oe,tagValues:se,tagRevivers:ue,createDictionary:ie,extendDictionary:ae,fetchUrlAsArrayBuffer:te,readBlobAsArrayBuffer:ne,chunkedProps:ye,otherSegments:me,segments:ke,tiffBlocks:Se,segmentsAndBlocks:Ce,tiffExtractables:Pe,inheritables:be,allFormatters:Ae,Options:we,parse:Ne}),ft={ifd0:!1,ifd1:!1,exif:!1,gps:!1,interop:!1,sanitize:!1,reviveValues:!0,translateKeys:!1,translateValues:!1,mergeOutput:!1};function ct(e,t,n){return n?t?t(e):e:(e&&e.then||(e=Promise.resolve(e)),t?e.then(t):e)}var lt=function(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];try{return Promise.resolve(e.apply(this,t))}catch(e){return Promise.reject(e)}}}((function(e){var t=new Be(ht);return ct(t.read(e),(function(){return ct(t.parse(),(function(e){if(e&&e.gps){var t=e.gps;return{latitude:t.latitude,longitude:t.longitude}}}))}))})),ht=m({},ft,{firstChunkSize:4e4,gps:[1,2,3,4]});function dt(e,t,n){return n?t?t(e):e:(e&&e.then||(e=Promise.resolve(e)),t?e.then(t):e)}function pt(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];try{return Promise.resolve(e.apply(this,t))}catch(e){return Promise.reject(e)}}}var gt=pt((function(e){return dt(this.thumbnail(e),(function(e){if(void 0!==e){var t=new Blob([e]);return URL.createObjectURL(t)}}))})),vt=pt((function(e){var t=new Be(yt);return dt(t.read(e),(function(){return dt(t.extractThumbnail(),(function(e){return e&&R?w.from(e):e}))}))})),yt=m({},ft,{tiff:!1,ifd1:!0,mergeOutput:!1});function mt(e,t,n){return n?t?t(e):e:(e&&e.then||(e=Promise.resolve(e)),t?e.then(t):e)}var kt=function(t){return mt(St(t),(function(t){return m({canvas:e.rotateCanvas,css:e.rotateCss},Pt[t])}))},St=function(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];try{return Promise.resolve(e.apply(this,t))}catch(e){return Promise.reject(e)}}}((function(e){var t=new Be(Ct);return mt(t.read(e),(function(){return mt(t.parse(),(function(e){if(e&&e.ifd0)return e.ifd0[274]}))}))})),Ct=m({},ft,{firstChunkSize:4e4,ifd0:[274]}),Pt=Object.freeze({1:{dimensionSwapped:!1,scaleX:1,scaleY:1,deg:0,rad:0},2:{dimensionSwapped:!1,scaleX:-1,scaleY:1,deg:0,rad:0},3:{dimensionSwapped:!1,scaleX:1,scaleY:1,deg:180,rad:180*Math.PI/180},4:{dimensionSwapped:!1,scaleX:-1,scaleY:1,deg:180,rad:180*Math.PI/180},5:{dimensionSwapped:!0,scaleX:1,scaleY:-1,deg:90,rad:90*Math.PI/180},6:{dimensionSwapped:!0,scaleX:1,scaleY:1,deg:90,rad:90*Math.PI/180},7:{dimensionSwapped:!0,scaleX:1,scaleY:-1,deg:270,rad:270*Math.PI/180},8:{dimensionSwapped:!0,scaleX:1,scaleY:1,deg:270,rad:270*Math.PI/180}});if(e.rotateCanvas=!0,e.rotateCss=!0,"object"==typeof navigator){var bt=navigator.userAgent;if(bt.includes("iPad")||bt.includes("iPhone")){var At=bt.match(/OS (\d+)_(\d+)/);if(At){var It=At[1],Tt=At[2],Ot=Number(It)+.1*Number(Tt);e.rotateCanvas=Ot<13.4,e.rotateCss=!1}}else if(bt.includes("OS X 10")){var Dt=bt.match(/OS X 10[_.](\d+)/)[1];e.rotateCanvas=e.rotateCss=Number(Dt)<15}if(bt.includes("Chrome/")){var wt=bt.match(/Chrome\/(\d+)/)[1];e.rotateCanvas=e.rotateCss=Number(wt)<81}else if(bt.includes("Firefox/")){var xt=bt.match(/Firefox\/(\d+)/)[1];e.rotateCanvas=e.rotateCss=Number(xt)<77}}function Rt(){}var Mt=function(e){a(s,e);var n=d(s);function s(){var e;t(this,s);for(var r=arguments.length,a=new Array(r),o=0;o<r;o++)a[o]=arguments[o];return i(l(e=n.call.apply(n,[this].concat(a))),"ranges",new Lt),0!==e.byteLength&&e.ranges.add(0,e.byteLength),e}return r(s,[{key:"_tryExtend",value:function(e,t,n){if(0===e&&0===this.byteLength&&n){var r=new DataView(n.buffer||n,n.byteOffset,n.byteLength);this._swapDataView(r)}else{var i=e+t;if(i>this.byteLength){var a=this._extend(i).dataView;this._swapDataView(a)}}}},{key:"_extend",value:function(e){var t;t=R?w.allocUnsafe(e):new Uint8Array(e);var n=new DataView(t.buffer,t.byteOffset,t.byteLength);return t.set(new Uint8Array(this.buffer,this.byteOffset,this.byteLength),0),{uintView:t,dataView:n}}},{key:"subarray",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return t=t||this._lengthToEnd(e),n&&this._tryExtend(e,t),this.ranges.add(e,t),p(o(s.prototype),"subarray",this).call(this,e,t)}},{key:"set",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];n&&this._tryExtend(t,e.byteLength,e);var r=p(o(s.prototype),"set",this).call(this,e,t);return this.ranges.add(t,r.byteLength),r}},{key:"ensureChunk",value:function(e,t){try{var n=this;if(!n.chunked)return;if(n.ranges.available(e,t))return;return function(e,t){if(!t)return e&&e.then?e.then(Rt):Promise.resolve()}(n.readChunk(e,t))}catch(e){return Promise.reject(e)}}},{key:"available",value:function(e,t){return this.ranges.available(e,t)}}]),s}(V),Lt=function(){function e(){t(this,e),i(this,"list",[])}return r(e,[{key:"length",get:function(){return this.list.length}},{key:"add",value:function(e,t){var n=e+t,r=this.list.filter((function(t){return Ut(e,t.offset,n)||Ut(e,t.end,n)}));if(r.length>0){e=Math.min.apply(Math,[e].concat(r.map((function(e){return e.offset})))),t=(n=Math.max.apply(Math,[n].concat(r.map((function(e){return e.end})))))-e;var i=r.shift();i.offset=e,i.length=t,i.end=n,this.list=this.list.filter((function(e){return!r.includes(e)}))}else this.list.push({offset:e,length:t,end:n})}},{key:"available",value:function(e,t){var n=e+t;return this.list.some((function(t){return t.offset<=e&&n<=t.end}))}}]),e}();function Ut(e,t,n){return e<=t&&t<=n}function Ft(){}function Et(e,t){if(!t)return e&&e.then?e.then(Ft):Promise.resolve()}var Bt=function(e){a(o,e);var n=d(o);function o(e,r){var a;return t(this,o),i(l(a=n.call(this,0)),"chunksRead",0),a.input=e,a.options=r,a}return r(o,[{key:"readWhole",value:function(){try{var e=this;return e.chunked=!1,Et(e.readChunk(e.nextChunkOffset))}catch(e){return Promise.reject(e)}}},{key:"readChunked",value:function(){try{var e=this;return e.chunked=!0,Et(e.readChunk(0,e.options.firstChunkSize))}catch(e){return Promise.reject(e)}}},{key:"readNextChunk",value:function(e){try{var t=this;if(void 0===e&&(e=t.nextChunkOffset),t.fullyRead)return t.chunksRead++,!1;var n=t.options.chunkSize;return r=t.readChunk(e,n),i=function(e){return!!e&&e.byteLength===n},a?i?i(r):r:(r&&r.then||(r=Promise.resolve(r)),i?r.then(i):r)}catch(e){return Promise.reject(e)}var r,i,a}},{key:"readChunk",value:function(e,t){try{var n=this;if(n.chunksRead++,0===(t=n.safeWrapAddress(e,t)))return;return n._readChunk(e,t)}catch(e){return Promise.reject(e)}}},{key:"safeWrapAddress",value:function(e,t){return void 0!==this.size&&e+t>this.size?Math.max(0,this.size-e):t}},{key:"nextChunkOffset",get:function(){if(0!==this.ranges.list.length)return this.ranges.list[0].length}},{key:"canReadNextChunk",get:function(){return this.chunksRead<this.options.chunkLimit}},{key:"fullyRead",get:function(){return void 0!==this.size&&this.nextChunkOffset===this.size}},{key:"read",value:function(){return this.options.chunked?this.readChunked():this.readWhole()}},{key:"close",value:function(){}}]),o}(Mt);function Nt(e,t,n){return n?t?t(e):e:(e&&e.then||(e=Promise.resolve(e)),t?e.then(t):e)}var Gt=function(e){a(i,e);var n=d(i);function i(){return t(this,i),n.apply(this,arguments)}return r(i,[{key:"readWhole",value:function(){try{var e=this;return e.chunked=!1,Nt(ne(e.input),(function(t){e._swapArrayBuffer(t)}))}catch(e){return Promise.reject(e)}}},{key:"readChunked",value:function(){return this.chunked=!0,this.size=this.input.size,p(o(i.prototype),"readChunked",this).call(this)}},{key:"_readChunk",value:function(e,t){try{var n=this,r=t?e+t:void 0,i=n.input.slice(e,r);return Nt(ne(i),(function(t){return n.set(t,e,!0)}))}catch(e){return Promise.reject(e)}}}]),i}(Bt);K.set("blob",Gt);var jt=Object.freeze({__proto__:null,default:ut,Exifr:Be,fileParsers:_,segmentParsers:W,fileReaders:K,tagKeys:oe,tagValues:se,tagRevivers:ue,createDictionary:ie,extendDictionary:ae,fetchUrlAsArrayBuffer:te,readBlobAsArrayBuffer:ne,chunkedProps:ye,otherSegments:me,segments:ke,tiffBlocks:Se,segmentsAndBlocks:Ce,tiffExtractables:Pe,inheritables:be,allFormatters:Ae,Options:we,parse:Ne,gps:lt,gpsOnlyOptions:ht,thumbnailUrl:gt,thumbnail:vt,thumbnailOnlyOptions:yt,rotation:kt,orientation:St,orientationOnlyOptions:Ct,rotations:Pt,get rotateCanvas(){return e.rotateCanvas},get rotateCss(){return e.rotateCss}});function Vt(e,t,n){return n?t?t(e):e:(e&&e.then||(e=Promise.resolve(e)),t?e.then(t):e)}var zt=function(e){a(i,e);var n=d(i);function i(){return t(this,i),n.apply(this,arguments)}return r(i,[{key:"readWhole",value:function(){try{var e=this;return e.chunked=!1,Vt(te(e.input),(function(t){t instanceof ArrayBuffer?e._swapArrayBuffer(t):t instanceof Uint8Array&&e._swapBuffer(t)}))}catch(e){return Promise.reject(e)}}},{key:"_readChunk",value:function(e,t){try{var n=this,r=t?e+t-1:void 0,i=n.options.httpHeaders||{};return(e||r)&&(i.range="bytes=".concat([e,r].join("-"))),Vt(I(n.input,{headers:i}),(function(r){return Vt(r.arrayBuffer(),(function(i){var a=i.byteLength;if(416!==r.status)return a!==t&&(n.size=e+a),n.set(i,e,!0)}))}))}catch(e){return Promise.reject(e)}}}]),i}(Bt);K.set("url",zt);function Ht(e,t,n){return n?t?t(e):e:(e&&e.then||(e=Promise.resolve(e)),t?e.then(t):e)}V.prototype.getUint64=function(e){var t=this.getUint32(e),n=this.getUint32(e+4);return t<1048575?t<<32|n:void 0!==typeof x?(console.warn("Using BigInt because of type 64uint but JS can only handle 53b numbers."),x(t)<<x(32)|x(n)):void F("Trying to read 64b value but JS can only handle 53b numbers.")};function _t(e,t,n){if(!e.s){if(n instanceof Wt){if(!n.s)return void(n.o=_t.bind(null,e,t));1&t&&(t=n.s),n=n.v}if(n&&n.then)return void n.then(_t.bind(null,e,t),_t.bind(null,e,2));e.s=t,e.v=n;var r=e.o;r&&r(e)}}var Wt=function(){function e(){}return e.prototype.then=function(t,n){var r=new e,i=this.s;if(i){var a=1&i?t:n;if(a){try{_t(r,1,a(this.v))}catch(e){_t(r,2,e)}return r}return this}return this.o=function(e){try{var i=e.v;1&e.s?_t(r,1,t?t(i):i):n?_t(r,1,n(i)):_t(r,2,i)}catch(e){_t(r,2,e)}},r},e}();function Kt(e){return e instanceof Wt&&1&e.s}function Xt(){}function Yt(e,t){if(!t)return e&&e.then?e.then(Xt):Promise.resolve()}var Jt=function(e){a(i,e);var n=d(i);function i(){return t(this,i),n.apply(this,arguments)}return r(i,[{key:"parse",value:function(){try{var e=this,t=e.file.getUint32(0),n=e.parseBoxHead(t);return r=function(e,t,n){for(var r;;){var i=e();if(Kt(i)&&(i=i.v),!i)return a;if(i.then){r=0;break}var a=n();if(a&&a.then){if(!Kt(a)){r=1;break}a=a.s}if(t){var o=t();if(o&&o.then&&!Kt(o)){r=2;break}}}var s=new Wt,u=_t.bind(null,s,2);return(0===r?i.then(c):1===r?a.then(f):o.then(l)).then(void 0,u),s;function f(r){a=r;do{if(t&&(o=t())&&o.then&&!Kt(o))return void o.then(l).then(void 0,u);if(!(i=e())||Kt(i)&&!i.v)return void _t(s,1,a);if(i.then)return void i.then(c).then(void 0,u);Kt(a=n())&&(a=a.v)}while(!a||!a.then);a.then(f).then(void 0,u)}function c(e){e?(a=n())&&a.then?a.then(f).then(void 0,u):f(a):_t(s,1,a)}function l(){(i=e())?i.then?i.then(c).then(void 0,u):c(i):_t(s,1,a)}}((function(){return"meta"!==n.kind}),void 0,(function(){return t+=n.length,Ht(e.file.ensureChunk(t,16),(function(){n=e.parseBoxHead(t)}))})),i=function(){return Ht(e.file.ensureChunk(n.offset,n.length),(function(){return e.parseBoxFullHead(n),e.parseSubBoxes(n),t=function(){return function(e){var t=e();if(t&&t.then)return t.then(Xt)}((function(){if(e.options.tiff.enabled)return Yt(e.findExif(n))}))},(r=function(){if(e.options.icc.enabled)return Yt(e.findIcc(n))}())&&r.then?r.then(t):t(r);var t,r}))},r&&r.then?r.then(i):i(r)}catch(e){return Promise.reject(e)}var r,i}},{key:"registerSegment",value:function(e,t,n){try{var r=this;return Ht(r.file.ensureChunk(t,n),(function(){var i=r.file.subarray(t,n);r.createParser(e,i)}))}catch(e){return Promise.reject(e)}}},{key:"findIcc",value:function(e){try{var t=this,n=t.findBox(e,"iprp");if(void 0===n)return;var r=t.findBox(n,"ipco");if(void 0===r)return;var i=t.findBox(r,"colr");if(void 0===i)return;return Yt(t.registerSegment("icc",i.offset+12,i.length))}catch(e){return Promise.reject(e)}}},{key:"findExif",value:function(e){try{var t=this,n=t.findBox(e,"iinf");if(void 0===n)return;var r=t.findBox(e,"iloc");if(void 0===r)return;var i=t.findExifLocIdInIinf(n),a=t.findExtentInIloc(r,i);if(void 0===a)return;var o=a[0],s=a[1];return Ht(t.file.ensureChunk(o,s),(function(){var e=4+t.file.getUint32(o);return o+=e,s-=e,Yt(t.registerSegment("tiff",o,s))}))}catch(e){return Promise.reject(e)}}},{key:"findExifLocIdInIinf",value:function(e){this.parseBoxFullHead(e);var t,n,r,i=e.start,a=this.file.getUint16(i);for(i+=2;a--;){if(t=this.parseBoxHead(i),this.parseBoxFullHead(t),n=t.start,t.version>=2&&(r=3===t.version?4:2,"Exif"===this.file.getString(n+r+2,4)))return this.file.getUintBytes(n,r);i+=t.length}}},{key:"get8bits",value:function(e){var t=this.file.getUint8(e);return[t>>4,15&t]}},{key:"findExtentInIloc",value:function(e,t){this.parseBoxFullHead(e);var n=e.start,r=this.get8bits(n++),i=r[0],a=r[1],o=this.get8bits(n++),s=o[0],u=o[1],f=2===e.version?4:2,c=1===e.version||2===e.version?2:0,l=u+i+a,h=2===e.version?4:2,d=this.file.getUintBytes(n,h);for(n+=h;d--;){var p=this.file.getUintBytes(n,f);n+=f+c+2+s;var g=this.file.getUint16(n);if(n+=2,p===t)return g>1&&console.warn("ILOC box has more than one extent but we're only processing one\nPlease create an issue at https://github.com/MikeKovarik/exifr with this file"),[this.file.getUintBytes(n+u,i),this.file.getUintBytes(n+u+i,a)];n+=g*l}}}],[{key:"canHandle",value:function(e,t){if(0!==t)return!1;var n=e.getUint16(2);if(n>50)return!1;for(var r=16,i=[];r<n;)i.push(e.getString(r,4)),r+=4;return i.includes(this.type)}}]),i}(function(e){a(i,e);var n=d(i);function i(){return t(this,i),n.apply(this,arguments)}return r(i,[{key:"parseBoxes",value:function(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=[];e<this.file.byteLength-4;){var n=this.parseBoxHead(e);if(t.push(n),0===n.length)break;e+=n.length}return t}},{key:"parseSubBoxes",value:function(e){e.boxes=this.parseBoxes(e.start)}},{key:"findBox",value:function(e,t){return void 0===e.boxes&&this.parseSubBoxes(e),e.boxes.find((function(e){return e.kind===t}))}},{key:"parseBoxHead",value:function(e){var t=this.file.getUint32(e),n=this.file.getString(e+4,4),r=e+8;return 1===t&&(t=this.file.getUint64(e+8),r+=8),{offset:e,length:t,kind:n,start:r}}},{key:"parseBoxFullHead",value:function(e){if(void 0===e.version){var t=this.file.getUint32(e.start);e.version=t>>24,e.start+=4}}}]),i}(Ve)),qt=function(e){a(r,e);var n=d(r);function r(){return t(this,r),n.apply(this,arguments)}return r}(Jt);i(qt,"type","heic");var Qt=function(e){a(r,e);var n=d(r);function r(){return t(this,r),n.apply(this,arguments)}return r}(Jt);i(Qt,"type","avif"),_.set("heic",qt),_.set("avif",Qt),ie(oe,["ifd0","ifd1"],[[256,"ImageWidth"],[257,"ImageHeight"],[258,"BitsPerSample"],[259,"Compression"],[262,"PhotometricInterpretation"],[270,"ImageDescription"],[271,"Make"],[272,"Model"],[273,"StripOffsets"],[274,"Orientation"],[277,"SamplesPerPixel"],[278,"RowsPerStrip"],[279,"StripByteCounts"],[282,"XResolution"],[283,"YResolution"],[284,"PlanarConfiguration"],[296,"ResolutionUnit"],[301,"TransferFunction"],[305,"Software"],[306,"ModifyDate"],[315,"Artist"],[316,"HostComputer"],[317,"Predictor"],[318,"WhitePoint"],[319,"PrimaryChromaticities"],[513,"ThumbnailOffset"],[514,"ThumbnailLength"],[529,"YCbCrCoefficients"],[530,"YCbCrSubSampling"],[531,"YCbCrPositioning"],[532,"ReferenceBlackWhite"],[700,"ApplicationNotes"],[33432,"Copyright"],[33723,"IPTC"],[34665,"ExifIFD"],[34675,"ICC"],[34853,"GpsIFD"],[330,"SubIFD"],[40965,"InteropIFD"],[40091,"XPTitle"],[40092,"XPComment"],[40093,"XPAuthor"],[40094,"XPKeywords"],[40095,"XPSubject"]]),ie(oe,"exif",[[33434,"ExposureTime"],[33437,"FNumber"],[34850,"ExposureProgram"],[34852,"SpectralSensitivity"],[34855,"ISO"],[34858,"TimeZoneOffset"],[34859,"SelfTimerMode"],[34864,"SensitivityType"],[34865,"StandardOutputSensitivity"],[34866,"RecommendedExposureIndex"],[34867,"ISOSpeed"],[34868,"ISOSpeedLatitudeyyy"],[34869,"ISOSpeedLatitudezzz"],[36864,"ExifVersion"],[36867,"DateTimeOriginal"],[36868,"CreateDate"],[36873,"GooglePlusUploadCode"],[36880,"OffsetTime"],[36881,"OffsetTimeOriginal"],[36882,"OffsetTimeDigitized"],[37121,"ComponentsConfiguration"],[37122,"CompressedBitsPerPixel"],[37377,"ShutterSpeedValue"],[37378,"ApertureValue"],[37379,"BrightnessValue"],[37380,"ExposureCompensation"],[37381,"MaxApertureValue"],[37382,"SubjectDistance"],[37383,"MeteringMode"],[37384,"LightSource"],[37385,"Flash"],[37386,"FocalLength"],[37393,"ImageNumber"],[37394,"SecurityClassification"],[37395,"ImageHistory"],[37396,"SubjectArea"],[37500,"MakerNote"],[37510,"UserComment"],[37520,"SubSecTime"],[37521,"SubSecTimeOriginal"],[37522,"SubSecTimeDigitized"],[37888,"AmbientTemperature"],[37889,"Humidity"],[37890,"Pressure"],[37891,"WaterDepth"],[37892,"Acceleration"],[37893,"CameraElevationAngle"],[40960,"FlashpixVersion"],[40961,"ColorSpace"],[40962,"ExifImageWidth"],[40963,"ExifImageHeight"],[40964,"RelatedSoundFile"],[41483,"FlashEnergy"],[41486,"FocalPlaneXResolution"],[41487,"FocalPlaneYResolution"],[41488,"FocalPlaneResolutionUnit"],[41492,"SubjectLocation"],[41493,"ExposureIndex"],[41495,"SensingMethod"],[41728,"FileSource"],[41729,"SceneType"],[41730,"CFAPattern"],[41985,"CustomRendered"],[41986,"ExposureMode"],[41987,"WhiteBalance"],[41988,"DigitalZoomRatio"],[41989,"FocalLengthIn35mmFormat"],[41990,"SceneCaptureType"],[41991,"GainControl"],[41992,"Contrast"],[41993,"Saturation"],[41994,"Sharpness"],[41996,"SubjectDistanceRange"],[42016,"ImageUniqueID"],[42032,"OwnerName"],[42033,"SerialNumber"],[42034,"LensInfo"],[42035,"LensMake"],[42036,"LensModel"],[42037,"LensSerialNumber"],[42080,"CompositeImage"],[42081,"CompositeImageCount"],[42082,"CompositeImageExposureTimes"],[42240,"Gamma"],[59932,"Padding"],[59933,"OffsetSchema"],[65e3,"OwnerName"],[65001,"SerialNumber"],[65002,"Lens"],[65100,"RawFile"],[65101,"Converter"],[65102,"WhiteBalance"],[65105,"Exposure"],[65106,"Shadows"],[65107,"Brightness"],[65108,"Contrast"],[65109,"Saturation"],[65110,"Sharpness"],[65111,"Smoothness"],[65112,"MoireFilter"],[40965,"InteropIFD"]]),ie(oe,"gps",[[0,"GPSVersionID"],[1,"GPSLatitudeRef"],[2,"GPSLatitude"],[3,"GPSLongitudeRef"],[4,"GPSLongitude"],[5,"GPSAltitudeRef"],[6,"GPSAltitude"],[7,"GPSTimeStamp"],[8,"GPSSatellites"],[9,"GPSStatus"],[10,"GPSMeasureMode"],[11,"GPSDOP"],[12,"GPSSpeedRef"],[13,"GPSSpeed"],[14,"GPSTrackRef"],[15,"GPSTrack"],[16,"GPSImgDirectionRef"],[17,"GPSImgDirection"],[18,"GPSMapDatum"],[19,"GPSDestLatitudeRef"],[20,"GPSDestLatitude"],[21,"GPSDestLongitudeRef"],[22,"GPSDestLongitude"],[23,"GPSDestBearingRef"],[24,"GPSDestBearing"],[25,"GPSDestDistanceRef"],[26,"GPSDestDistance"],[27,"GPSProcessingMethod"],[28,"GPSAreaInformation"],[29,"GPSDateStamp"],[30,"GPSDifferential"],[31,"GPSHPositioningError"]]),ie(se,["ifd0","ifd1"],[[274,{1:"Horizontal (normal)",2:"Mirror horizontal",3:"Rotate 180",4:"Mirror vertical",5:"Mirror horizontal and rotate 270 CW",6:"Rotate 90 CW",7:"Mirror horizontal and rotate 90 CW",8:"Rotate 270 CW"}],[296,{1:"None",2:"inches",3:"cm"}]]);var Zt=ie(se,"exif",[[34850,{0:"Not defined",1:"Manual",2:"Normal program",3:"Aperture priority",4:"Shutter priority",5:"Creative program",6:"Action program",7:"Portrait mode",8:"Landscape mode"}],[37121,{0:"-",1:"Y",2:"Cb",3:"Cr",4:"R",5:"G",6:"B"}],[37383,{0:"Unknown",1:"Average",2:"CenterWeightedAverage",3:"Spot",4:"MultiSpot",5:"Pattern",6:"Partial",255:"Other"}],[37384,{0:"Unknown",1:"Daylight",2:"Fluorescent",3:"Tungsten (incandescent light)",4:"Flash",9:"Fine weather",10:"Cloudy weather",11:"Shade",12:"Daylight fluorescent (D 5700 - 7100K)",13:"Day white fluorescent (N 4600 - 5400K)",14:"Cool white fluorescent (W 3900 - 4500K)",15:"White fluorescent (WW 3200 - 3700K)",17:"Standard light A",18:"Standard light B",19:"Standard light C",20:"D55",21:"D65",22:"D75",23:"D50",24:"ISO studio tungsten",255:"Other"}],[37385,{0:"Flash did not fire",1:"Flash fired",5:"Strobe return light not detected",7:"Strobe return light detected",9:"Flash fired, compulsory flash mode",13:"Flash fired, compulsory flash mode, return light not detected",15:"Flash fired, compulsory flash mode, return light detected",16:"Flash did not fire, compulsory flash mode",24:"Flash did not fire, auto mode",25:"Flash fired, auto mode",29:"Flash fired, auto mode, return light not detected",31:"Flash fired, auto mode, return light detected",32:"No flash function",65:"Flash fired, red-eye reduction mode",69:"Flash fired, red-eye reduction mode, return light not detected",71:"Flash fired, red-eye reduction mode, return light detected",73:"Flash fired, compulsory flash mode, red-eye reduction mode",77:"Flash fired, compulsory flash mode, red-eye reduction mode, return light not detected",79:"Flash fired, compulsory flash mode, red-eye reduction mode, return light detected",89:"Flash fired, auto mode, red-eye reduction mode",93:"Flash fired, auto mode, return light not detected, red-eye reduction mode",95:"Flash fired, auto mode, return light detected, red-eye reduction mode"}],[41495,{1:"Not defined",2:"One-chip color area sensor",3:"Two-chip color area sensor",4:"Three-chip color area sensor",5:"Color sequential area sensor",7:"Trilinear sensor",8:"Color sequential linear sensor"}],[41728,{1:"Film Scanner",2:"Reflection Print Scanner",3:"Digital Camera"}],[41729,{1:"Directly photographed"}],[41985,{0:"Normal",1:"Custom",2:"HDR (no original saved)",3:"HDR (original saved)",4:"Original (for HDR)",6:"Panorama",7:"Portrait HDR",8:"Portrait"}],[41986,{0:"Auto",1:"Manual",2:"Auto bracket"}],[41987,{0:"Auto",1:"Manual"}],[41990,{0:"Standard",1:"Landscape",2:"Portrait",3:"Night",4:"Other"}],[41991,{0:"None",1:"Low gain up",2:"High gain up",3:"Low gain down",4:"High gain down"}],[41996,{0:"Unknown",1:"Macro",2:"Close",3:"Distant"}],[42080,{0:"Unknown",1:"Not a Composite Image",2:"General Composite Image",3:"Composite Image Captured While Shooting"}]]),$t={1:"No absolute unit of measurement",2:"Inch",3:"Centimeter"};Zt.set(37392,$t),Zt.set(41488,$t);var en={0:"Normal",1:"Low",2:"High"};function tn(e){return"object"==typeof e&&void 0!==e.length?e[0]:e}function nn(e){var t=S(e).slice(1);return t[1]>15&&(t=t.map((function(e){return String.fromCharCode(e)}))),"0"!==t[2]&&0!==t[2]||t.pop(),t.join(".")}function rn(e){if("string"==typeof e){var t=e.trim().split(/[-: ]/g).map(Number),n=t[0],r=t[1],i=t[2],a=t[3],o=t[4],s=t[5],u=new Date(n,r-1,i);return isNaN(a)||isNaN(o)||isNaN(s)||(u.setHours(a),u.setMinutes(o),u.setSeconds(s)),isNaN(+u)?e:u}}function an(e){if("string"==typeof e)return e;var t=[];if(0===e[1]&&0===e[e.length-1])for(var n=0;n<e.length;n+=2)t.push(on(e[n+1],e[n]));else for(var r=0;r<e.length;r+=2)t.push(on(e[r],e[r+1]));return E(String.fromCodePoint.apply(String,t))}function on(e,t){return e<<8|t}Zt.set(41992,en),Zt.set(41993,en),Zt.set(41994,en),ie(ue,["ifd0","ifd1"],[[50827,function(e){return"string"!=typeof e?j(e):e}],[306,rn],[40091,an],[40092,an],[40093,an],[40094,an],[40095,an]]),ie(ue,"exif",[[40960,nn],[36864,nn],[36867,rn],[36868,rn],[40962,tn],[40963,tn]]),ie(ue,"gps",[[0,function(e){return S(e).join(".")}],[7,function(e){return S(e).join(":")}]]);var sn="http://ns.adobe.com/",un="http://ns.adobe.com/xmp/extension/",fn=function(e){a(i,e);var n=d(i);function i(){return t(this,i),n.apply(this,arguments)}return r(i,[{key:"normalizeInput",value:function(e){return"string"==typeof e?e:V.from(e).getString()}},{key:"parse",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.chunk;if(!this.localOptions.parse)return e;e=Pn(e);var t=dn.findAll(e,"rdf","Description");0===t.length&&t.push(new dn("rdf","Description",void 0,e));var n={},r=t;Array.isArray(r)||("function"==typeof r.entries&&(r=r.entries()),r=S(r));for(var i=0;i<r.length;i++){var a=r[i],o=a.properties;Array.isArray(o)||("function"==typeof o.entries&&(o=o.entries()),o=S(o));for(var s=0;s<o.length;s++){var u=o[s];pn(u,yn(u.ns,n))}}return cn(n)}},{key:"assignToOutput",value:function(e,t){if(this.localOptions.parse){var n=y(t);Array.isArray(n)||("function"==typeof n.entries&&(n=n.entries()),n=S(n));for(var r=0;r<n.length;r++){var i=n[r],a=i[0],o=i[1];switch(a){case"tiff":this.assignObjectToOutput(e,"ifd0",o);break;case"exif":this.assignObjectToOutput(e,"exif",o);break;case"xmlns":break;default:this.assignObjectToOutput(e,a,o)}}}else e.xmp=t}}],[{key:"canHandle",value:function(e,t){return 225===e.getUint8(t+1)&&1752462448===e.getUint32(t+4)&&e.getString(t+4,sn.length)===sn}},{key:"headerLength",value:function(e,t){return e.getString(t+4,un.length)===un?79:4+"http://ns.adobe.com/xap/1.0/".length+1}},{key:"findPosition",value:function(e,t){var n=p(o(i),"findPosition",this).call(this,e,t);return n.multiSegment=n.extended=79===n.headerLength,n.multiSegment?(n.chunkCount=e.getUint8(t+72),n.chunkNumber=e.getUint8(t+76),0!==e.getUint8(t+77)&&n.chunkNumber++):(n.chunkCount=1/0,n.chunkNumber=-1),n}},{key:"handleMultiSegments",value:function(e){return e.map((function(e){return e.chunk.getString()})).join("")}}]),i}(ze);function cn(e){for(var t in e)void 0===(e[t]=M(e[t]))&&delete e[t];return M(e)}i(fn,"type","xmp"),i(fn,"multiSegment",!0),W.set("xmp",fn);var ln=function(){function e(n,r,i){t(this,e),this.ns=n,this.name=r,this.value=i}return r(e,[{key:"serialize",value:function(){return this.value}}],[{key:"findAll",value:function(t){return mn(t,/([a-zA-Z0-9-]+):([a-zA-Z0-9-]+)=("[^"]*"|'[^']*')/gm).map(e.unpackMatch)}},{key:"unpackMatch",value:function(t){var n=t[1],r=t[2],i=t[3].slice(1,-1);return new e(n,r,i=kn(i))}}]),e}(),hn="[\\w\\d-]+",dn=function(){function e(n,r,i,a){t(this,e),this.ns=n,this.name=r,this.attrString=i,this.innerXml=a,this.attrs=ln.findAll(i),this.children=e.findAll(a),this.value=0===this.children.length?kn(a):void 0,this.properties=[].concat(this.attrs,this.children)}return r(e,[{key:"isPrimitive",get:function(){return void 0!==this.value&&0===this.attrs.length&&0===this.children.length}},{key:"isListContainer",get:function(){return 1===this.children.length&&this.children[0].isList}},{key:"isList",get:function(){var e=this.ns,t=this.name;return"rdf"===e&&("Seq"===t||"Bag"===t||"Alt"===t)}},{key:"isListItem",get:function(){return"rdf"===this.ns&&"li"===this.name}},{key:"serialize",value:function(){if(0!==this.properties.length||void 0!==this.value){if(this.isPrimitive)return this.value;if(this.isListContainer)return this.children[0].serialize();if(this.isList)return vn(this.children.map(gn));if(this.isListItem&&1===this.children.length&&0===this.attrs.length)return this.children[0].serialize();var e={},t=this.properties;Array.isArray(t)||("function"==typeof t.entries&&(t=t.entries()),t=S(t));for(var n=0;n<t.length;n++){pn(t[n],e)}return void 0!==this.value&&(e.value=this.value),M(e)}}}],[{key:"findAll",value:function(t,n,r){if(void 0!==n||void 0!==r){n=n||hn,r=r||hn;var i=new RegExp("<(".concat(n,"):(").concat(r,")(#\\d+)?((\\s+?[\\w\\d-:]+=(\"[^\"]*\"|'[^']*'))*\\s*)(\\/>|>([\\s\\S]*?)<\\/\\1:\\2\\3>)"),"gm")}else i=/<([\w\d-]+):([\w\d-]+)(#\d+)?((\s+?[\w\d-:]+=("[^"]*"|'[^']*'))*\s*)(\/>|>([\s\S]*?)<\/\1:\2\3>)/gm;return mn(t,i).map(e.unpackMatch)}},{key:"unpackMatch",value:function(t){return new e(t[1],t[2],t[4],t[8])}}]),e}();function pn(e,t){var n=e.serialize();void 0!==n&&(t[e.name]=n)}var gn=function(e){return e.serialize()},vn=function(e){return 1===e.length?e[0]:e},yn=function(e,t){return t[e]?t[e]:t[e]={}};function mn(e,t){var n,r=[];if(!e)return r;for(;null!==(n=t.exec(e));)r.push(n);return r}function kn(e){if(!function(e){return null==e||"null"===e||"undefined"===e||""===e||""===e.trim()}(e)){var t=Number(e);if(!isNaN(t))return t;var n=e.toLowerCase();return"true"===n||"false"!==n&&e.trim()}}var Sn=["rdf:li","rdf:Seq","rdf:Bag","rdf:Alt","rdf:Description"],Cn=new RegExp("(<|\\/)(".concat(Sn.join("|"),")"),"g");function Pn(e){var t={},n={},r=Sn;Array.isArray(r)||("function"==typeof r.entries&&(r=r.entries()),r=S(r));for(var i=0;i<r.length;i++){var a=r[i];t[a]=[],n[a]=0}return e.replace(Cn,(function(e,r,i){if("<"===r){var a=++n[i];return t[i].push(a),"".concat(e,"#").concat(a)}var o=t[i].pop();return"".concat(e,"#").concat(o)}))}var bn=Object.freeze({__proto__:null,default:jt,Exifr:Be,fileParsers:_,segmentParsers:W,fileReaders:K,tagKeys:oe,tagValues:se,tagRevivers:ue,createDictionary:ie,extendDictionary:ae,fetchUrlAsArrayBuffer:te,readBlobAsArrayBuffer:ne,chunkedProps:ye,otherSegments:me,segments:ke,tiffBlocks:Se,segmentsAndBlocks:Ce,tiffExtractables:Pe,inheritables:be,allFormatters:Ae,Options:we,parse:Ne,gps:lt,gpsOnlyOptions:ht,thumbnailUrl:gt,thumbnail:vt,thumbnailOnlyOptions:yt,rotation:kt,orientation:St,orientationOnlyOptions:Ct,rotations:Pt,get rotateCanvas(){return e.rotateCanvas},get rotateCss(){return e.rotateCss}});function An(e,t,n){return n?t?t(e):e:(e&&e.then||(e=Promise.resolve(e)),t?e.then(t):e)}var In=Rn((function(e,t,n){var r=n[e];return r.enabled=!0,r.parse=!0,W.get(e).parse(t,r)})),Tn="undefined"!=typeof Symbol?Symbol.iterator||(Symbol.iterator=Symbol("Symbol.iterator")):"@@iterator";function On(e,t,n){if(!e.s){if(n instanceof Dn){if(!n.s)return void(n.o=On.bind(null,e,t));1&t&&(t=n.s),n=n.v}if(n&&n.then)return void n.then(On.bind(null,e,t),On.bind(null,e,2));e.s=t,e.v=n;var r=e.o;r&&r(e)}}var Dn=function(){function e(){}return e.prototype.then=function(t,n){var r=new e,i=this.s;if(i){var a=1&i?t:n;if(a){try{On(r,1,a(this.v))}catch(e){On(r,2,e)}return r}return this}return this.o=function(e){try{var i=e.v;1&e.s?On(r,1,t?t(i):i):n?On(r,1,n(i)):On(r,2,i)}catch(e){On(r,2,e)}},r},e}();function wn(e){return e instanceof Dn&&1&e.s}function xn(e,t,n){if("function"==typeof e[Tn]){var r,i,a,o=e[Tn]();if(function e(s){try{for(;!((r=o.next()).done||n&&n());)if((s=t(r.value))&&s.then){if(!wn(s))return void s.then(e,a||(a=On.bind(null,i=new Dn,2)));s=s.v}i?On(i,1,s):i=s}catch(e){On(i||(i=new Dn),2,e)}}(),o.return){var s=function(e){try{r.done||o.return()}catch(e){}return e};if(i&&i.then)return i.then(s,(function(e){throw s(e)}));s()}return i}if(!("length"in e))throw new TypeError("Object is not iterable");for(var u=[],f=0;f<e.length;f++)u.push(e[f]);return function(e,t,n){var r,i,a=-1;return function o(s){try{for(;++a<e.length&&(!n||!n());)if((s=t(a))&&s.then){if(!wn(s))return void s.then(o,i||(i=On.bind(null,r=new Dn,2)));s=s.v}r?On(r,1,s):r=s}catch(e){On(r||(r=new Dn),2,e)}}(),r}(u,(function(e){return t(u[e])}),n)}function Rn(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];try{return Promise.resolve(e.apply(this,t))}catch(e){return Promise.reject(e)}}}var Mn=Rn((function(e,t,n){var r=new we(t);return r.chunked=!1,void 0===n&&"string"==typeof e&&(n=function(e){var t=e.toLowerCase().split(".").pop();if(function(e){return"exif"===e||"tiff"===e||"tif"===e}(t))return"tiff";if(Ln.includes(t))return t}(e)),An($(e,r),(function(e){var t,i,a=!1;return n?Ln.includes(n)?In(n,e,r):void F("Invalid segment type"):function(e){var t=e.getString(0,50).trim();return t.includes("<?xpacket")||t.includes("<x:")}(e)?In("xmp",e,r):(t=xn(W,(function(t){var n=t[0];if(Ln.includes(n))return An(In(n,e,r).catch(Un),(function(e){if(e)return a=!0,e}))}),(function(){return a})),i=function(e){if(a)return e;F("Unknown file format")},t&&t.then?t.then(i):i(t))}))})),Ln=["xmp","icc","iptc","tiff"],Un=function(){};var Fn=function(e){a(i,e);var n=d(i);function i(){var e;t(this,i);for(var r=arguments.length,a=new Array(r),o=0;o<r;o++)a[o]=arguments[o];return(e=n.call.apply(n,[this].concat(a))).input=e.input.replace(/^data:([^;]+);base64,/gim,""),e.size=e.input.length/4*3,e.input.endsWith("==")?e.size-=2:e.input.endsWith("=")&&(e.size-=1),e}return r(i,[{key:"_readChunk",value:function(e,t){try{var n,r,i=this,a=i.input;void 0===e?(e=0,n=0,r=0):r=e-(n=4*Math.floor(e/3))/4*3,void 0===t&&(t=i.size);var o=e+t,s=n+4*Math.ceil(o/3);a=a.slice(n,s);var u=Math.min(t,i.size-e);if(R){var f=w.from(a,"base64").slice(r,r+u);return i.set(f,e,!0)}for(var c=i.subarray(e,u,!0),l=atob(a),h=c.toUint8(),d=0;d<u;d++)h[d]=l.charCodeAt(r+d);return c}catch(e){return Promise.reject(e)}}}]),i}(Bt);function En(e,t,n){return n?t?t(e):e:(e&&e.then||(e=Promise.resolve(e)),t?e.then(t):e)}function Bn(){}K.set("base64",Fn);var Nn=function(e){a(i,e);var n=d(i);function i(){return t(this,i),n.apply(this,arguments)}return r(i,[{key:"extendOptions",value:function(e){var t=e.ifd0,n=e.xmp,r=e.iptc,i=e.icc;n.enabled&&t.deps.add(le),r.enabled&&t.deps.add(he),i.enabled&&t.deps.add(de),t.finalizeFilters()}},{key:"parse",value:function(){try{var e=this,t=e.options,n=t.tiff,r=t.xmp,i=t.iptc,a=t.icc;return function(e){var t=e();if(t&&t.then)return t.then(Bn)}((function(){if(n.enabled||r.enabled||i.enabled||a.enabled){var t=Math.max(B(e.options),e.options.chunkSize);return En(e.file.ensureChunk(0,t),(function(){return e.createParser("tiff",e.file),e.parsers.tiff.parseHeader(),En(e.parsers.tiff.parseIfd0Block(),(function(){e.adaptTiffPropAsSegment("xmp"),e.adaptTiffPropAsSegment("iptc"),e.adaptTiffPropAsSegment("icc")}))}))}}))}catch(e){return Promise.reject(e)}}},{key:"adaptTiffPropAsSegment",value:function(e){if(this.parsers.tiff[e]){var t=this.parsers.tiff[e];this.injectSegment(e,t)}}}],[{key:"canHandle",value:function(e,t){return 18761===t||19789===t}}]),i}(Ve);function Gn(){}i(Nn,"type","tiff"),_.set("tiff",Nn);function jn(e,t,n){return n?t?t(e):e:(e&&e.then||(e=Promise.resolve(e)),t?e.then(t):e)}var Vn="XML:com.adobe.xmp";var zn="ihdr",Hn="iccp",_n="text",Wn="itxt",Kn=[zn,Hn,_n,Wn,"exif"],Xn=function(e){a(o,e);var n=d(o);function o(){var e;t(this,o);for(var r=arguments.length,a=new Array(r),s=0;s<r;s++)a[s]=arguments[s];return i(l(e=n.call.apply(n,[this].concat(a))),"catchError",(function(t){return e.errors.push(t)})),i(l(e),"metaChunks",[]),i(l(e),"unknownChunks",[]),e}return r(o,[{key:"parse",value:function(){try{var e=this,t=e.file;return jn(e.findPngChunksInRange("PNG\r\n\n".length,t.byteLength),(function(){return jn(e.readSegments(e.metaChunks),(function(){return e.findIhdr(),e.parseTextChunks(),jn(e.findExif().catch(e.catchError),(function(){return jn(e.findXmp().catch(e.catchError),(function(){return function(e,t){if(!t)return e&&e.then?e.then(Gn):Promise.resolve()}(e.findIcc().catch(e.catchError))}))}))}))}))}catch(e){return Promise.reject(e)}}},{key:"findPngChunksInRange",value:function(e,t){try{for(var n=this,r=n.file;e<t;){var i=r.getUint32(e),a=r.getUint32(e+4),o=r.getString(e+4,4).toLowerCase(),s=i+4+4+4,u={type:o,offset:e,length:s,start:e+4+4,size:i,marker:a};Kn.includes(o)?n.metaChunks.push(u):n.unknownChunks.push(u),e+=s}return jn()}catch(e){return Promise.reject(e)}}},{key:"parseTextChunks",value:function(){var e=this.metaChunks.filter((function(e){return e.type===_n}));Array.isArray(e)||("function"==typeof e.entries&&(e=e.entries()),e=S(e));for(var t=0;t<e.length;t++){var n=e[t],r=this.file.getString(n.start,n.size).split("\0"),i=r[0],a=r[1];this.injectKeyValToIhdr(i,a)}}},{key:"injectKeyValToIhdr",value:function(e,t){var n=this.parsers.ihdr;n&&n.raw.set(e,t)}},{key:"findIhdr",value:function(){var e=this.metaChunks.find((function(e){return e.type===zn}));e&&!1!==this.options.ihdr.enabled&&this.createParser(zn,e.chunk)}},{key:"findExif",value:function(){try{var e=this.metaChunks.find((function(e){return"exif"===e.type}));if(!e)return;return this.injectSegment("tiff",e.chunk),jn()}catch(e){return Promise.reject(e)}}},{key:"findXmp",value:function(){try{var e=this.metaChunks.filter((function(e){return e.type===Wn}));Array.isArray(e)||("function"==typeof e.entries&&(e=e.entries()),e=S(e));for(var t=0;t<e.length;t++){var n=e[t];n.chunk.getString(0,Vn.length)===Vn&&this.injectSegment("xmp",n.chunk)}return jn()}catch(e){return Promise.reject(e)}}},{key:"findIcc",value:function(){try{var e=this,t=e.metaChunks.find((function(e){return e.type===Hn}));if(!t)return;for(var n=t.chunk,r=n.getUint8Array(0,81),i=0;i<80&&0!==r[i];)i++;var a=i+2,o=n.getString(0,i);return e.injectKeyValToIhdr("ProfileName",o),function(e){var t=e();if(t&&t.then)return t.then(Gn)}((function(){if(D)return jn(undefined,(function(t){var r=n.getUint8Array(a);r=t.inflateSync(r),e.injectSegment("icc",r)}))}))}catch(e){return Promise.reject(e)}}}],[{key:"canHandle",value:function(e,t){return 35152===t&&2303741511===e.getUint32(0)&&218765834===e.getUint32(4)}}]),o}(Ve);i(Xn,"type","png"),_.set("png",Xn),ie(oe,"interop",[[1,"InteropIndex"],[2,"InteropVersion"],[4096,"RelatedImageFileFormat"],[4097,"RelatedImageWidth"],[4098,"RelatedImageHeight"]]),ae(oe,"ifd0",[[11,"ProcessingSoftware"],[254,"SubfileType"],[255,"OldSubfileType"],[263,"Thresholding"],[264,"CellWidth"],[265,"CellLength"],[266,"FillOrder"],[269,"DocumentName"],[280,"MinSampleValue"],[281,"MaxSampleValue"],[285,"PageName"],[286,"XPosition"],[287,"YPosition"],[290,"GrayResponseUnit"],[297,"PageNumber"],[321,"HalftoneHints"],[322,"TileWidth"],[323,"TileLength"],[332,"InkSet"],[337,"TargetPrinter"],[18246,"Rating"],[18249,"RatingPercent"],[33550,"PixelScale"],[34264,"ModelTransform"],[34377,"PhotoshopSettings"],[50706,"DNGVersion"],[50707,"DNGBackwardVersion"],[50708,"UniqueCameraModel"],[50709,"LocalizedCameraModel"],[50736,"DNGLensInfo"],[50739,"ShadowScale"],[50740,"DNGPrivateData"],[33920,"IntergraphMatrix"],[33922,"ModelTiePoint"],[34118,"SEMInfo"],[34735,"GeoTiffDirectory"],[34736,"GeoTiffDoubleParams"],[34737,"GeoTiffAsciiParams"],[50341,"PrintIM"],[50721,"ColorMatrix1"],[50722,"ColorMatrix2"],[50723,"CameraCalibration1"],[50724,"CameraCalibration2"],[50725,"ReductionMatrix1"],[50726,"ReductionMatrix2"],[50727,"AnalogBalance"],[50728,"AsShotNeutral"],[50729,"AsShotWhiteXY"],[50730,"BaselineExposure"],[50731,"BaselineNoise"],[50732,"BaselineSharpness"],[50734,"LinearResponseLimit"],[50735,"CameraSerialNumber"],[50741,"MakerNoteSafety"],[50778,"CalibrationIlluminant1"],[50779,"CalibrationIlluminant2"],[50781,"RawDataUniqueID"],[50827,"OriginalRawFileName"],[50828,"OriginalRawFileData"],[50831,"AsShotICCProfile"],[50832,"AsShotPreProfileMatrix"],[50833,"CurrentICCProfile"],[50834,"CurrentPreProfileMatrix"],[50879,"ColorimetricReference"],[50885,"SRawType"],[50898,"PanasonicTitle"],[50899,"PanasonicTitle2"],[50931,"CameraCalibrationSig"],[50932,"ProfileCalibrationSig"],[50933,"ProfileIFD"],[50934,"AsShotProfileName"],[50936,"ProfileName"],[50937,"ProfileHueSatMapDims"],[50938,"ProfileHueSatMapData1"],[50939,"ProfileHueSatMapData2"],[50940,"ProfileToneCurve"],[50941,"ProfileEmbedPolicy"],[50942,"ProfileCopyright"],[50964,"ForwardMatrix1"],[50965,"ForwardMatrix2"],[50966,"PreviewApplicationName"],[50967,"PreviewApplicationVersion"],[50968,"PreviewSettingsName"],[50969,"PreviewSettingsDigest"],[50970,"PreviewColorSpace"],[50971,"PreviewDateTime"],[50972,"RawImageDigest"],[50973,"OriginalRawFileDigest"],[50981,"ProfileLookTableDims"],[50982,"ProfileLookTableData"],[51043,"TimeCodes"],[51044,"FrameRate"],[51058,"TStop"],[51081,"ReelName"],[51089,"OriginalDefaultFinalSize"],[51090,"OriginalBestQualitySize"],[51091,"OriginalDefaultCropSize"],[51105,"CameraLabel"],[51107,"ProfileHueSatMapEncoding"],[51108,"ProfileLookTableEncoding"],[51109,"BaselineExposureOffset"],[51110,"DefaultBlackRender"],[51111,"NewRawImageDigest"],[51112,"RawToPreviewGain"]]);var Yn=[[273,"StripOffsets"],[279,"StripByteCounts"],[288,"FreeOffsets"],[289,"FreeByteCounts"],[291,"GrayResponseCurve"],[292,"T4Options"],[293,"T6Options"],[300,"ColorResponseUnit"],[320,"ColorMap"],[324,"TileOffsets"],[325,"TileByteCounts"],[326,"BadFaxLines"],[327,"CleanFaxData"],[328,"ConsecutiveBadFaxLines"],[330,"SubIFD"],[333,"InkNames"],[334,"NumberofInks"],[336,"DotRange"],[338,"ExtraSamples"],[339,"SampleFormat"],[340,"SMinSampleValue"],[341,"SMaxSampleValue"],[342,"TransferRange"],[343,"ClipPath"],[344,"XClipPathUnits"],[345,"YClipPathUnits"],[346,"Indexed"],[347,"JPEGTables"],[351,"OPIProxy"],[400,"GlobalParametersIFD"],[401,"ProfileType"],[402,"FaxProfile"],[403,"CodingMethods"],[404,"VersionYear"],[405,"ModeNumber"],[433,"Decode"],[434,"DefaultImageColor"],[435,"T82Options"],[437,"JPEGTables"],[512,"JPEGProc"],[515,"JPEGRestartInterval"],[517,"JPEGLosslessPredictors"],[518,"JPEGPointTransforms"],[519,"JPEGQTables"],[520,"JPEGDCTables"],[521,"JPEGACTables"],[559,"StripRowCounts"],[999,"USPTOMiscellaneous"],[18247,"XP_DIP_XML"],[18248,"StitchInfo"],[28672,"SonyRawFileType"],[28688,"SonyToneCurve"],[28721,"VignettingCorrection"],[28722,"VignettingCorrParams"],[28724,"ChromaticAberrationCorrection"],[28725,"ChromaticAberrationCorrParams"],[28726,"DistortionCorrection"],[28727,"DistortionCorrParams"],[29895,"SonyCropTopLeft"],[29896,"SonyCropSize"],[32781,"ImageID"],[32931,"WangTag1"],[32932,"WangAnnotation"],[32933,"WangTag3"],[32934,"WangTag4"],[32953,"ImageReferencePoints"],[32954,"RegionXformTackPoint"],[32955,"WarpQuadrilateral"],[32956,"AffineTransformMat"],[32995,"Matteing"],[32996,"DataType"],[32997,"ImageDepth"],[32998,"TileDepth"],[33300,"ImageFullWidth"],[33301,"ImageFullHeight"],[33302,"TextureFormat"],[33303,"WrapModes"],[33304,"FovCot"],[33305,"MatrixWorldToScreen"],[33306,"MatrixWorldToCamera"],[33405,"Model2"],[33421,"CFARepeatPatternDim"],[33422,"CFAPattern2"],[33423,"BatteryLevel"],[33424,"KodakIFD"],[33445,"MDFileTag"],[33446,"MDScalePixel"],[33447,"MDColorTable"],[33448,"MDLabName"],[33449,"MDSampleInfo"],[33450,"MDPrepDate"],[33451,"MDPrepTime"],[33452,"MDFileUnits"],[33589,"AdventScale"],[33590,"AdventRevision"],[33628,"UIC1Tag"],[33629,"UIC2Tag"],[33630,"UIC3Tag"],[33631,"UIC4Tag"],[33918,"IntergraphPacketData"],[33919,"IntergraphFlagRegisters"],[33921,"INGRReserved"],[34016,"Site"],[34017,"ColorSequence"],[34018,"IT8Header"],[34019,"RasterPadding"],[34020,"BitsPerRunLength"],[34021,"BitsPerExtendedRunLength"],[34022,"ColorTable"],[34023,"ImageColorIndicator"],[34024,"BackgroundColorIndicator"],[34025,"ImageColorValue"],[34026,"BackgroundColorValue"],[34027,"PixelIntensityRange"],[34028,"TransparencyIndicator"],[34029,"ColorCharacterization"],[34030,"HCUsage"],[34031,"TrapIndicator"],[34032,"CMYKEquivalent"],[34152,"AFCP_IPTC"],[34232,"PixelMagicJBIGOptions"],[34263,"JPLCartoIFD"],[34306,"WB_GRGBLevels"],[34310,"LeafData"],[34687,"TIFF_FXExtensions"],[34688,"MultiProfiles"],[34689,"SharedData"],[34690,"T88Options"],[34732,"ImageLayer"],[34750,"JBIGOptions"],[34856,"Opto-ElectricConvFactor"],[34857,"Interlace"],[34908,"FaxRecvParams"],[34909,"FaxSubAddress"],[34910,"FaxRecvTime"],[34929,"FedexEDR"],[34954,"LeafSubIFD"],[37387,"FlashEnergy"],[37388,"SpatialFrequencyResponse"],[37389,"Noise"],[37390,"FocalPlaneXResolution"],[37391,"FocalPlaneYResolution"],[37392,"FocalPlaneResolutionUnit"],[37397,"ExposureIndex"],[37398,"TIFF-EPStandardID"],[37399,"SensingMethod"],[37434,"CIP3DataFile"],[37435,"CIP3Sheet"],[37436,"CIP3Side"],[37439,"StoNits"],[37679,"MSDocumentText"],[37680,"MSPropertySetStorage"],[37681,"MSDocumentTextPosition"],[37724,"ImageSourceData"],[40965,"InteropIFD"],[40976,"SamsungRawPointersOffset"],[40977,"SamsungRawPointersLength"],[41217,"SamsungRawByteOrder"],[41218,"SamsungRawUnknown"],[41484,"SpatialFrequencyResponse"],[41485,"Noise"],[41489,"ImageNumber"],[41490,"SecurityClassification"],[41491,"ImageHistory"],[41494,"TIFF-EPStandardID"],[41995,"DeviceSettingDescription"],[42112,"GDALMetadata"],[42113,"GDALNoData"],[44992,"ExpandSoftware"],[44993,"ExpandLens"],[44994,"ExpandFilm"],[44995,"ExpandFilterLens"],[44996,"ExpandScanner"],[44997,"ExpandFlashLamp"],[46275,"HasselbladRawImage"],[48129,"PixelFormat"],[48130,"Transformation"],[48131,"Uncompressed"],[48132,"ImageType"],[48256,"ImageWidth"],[48257,"ImageHeight"],[48258,"WidthResolution"],[48259,"HeightResolution"],[48320,"ImageOffset"],[48321,"ImageByteCount"],[48322,"AlphaOffset"],[48323,"AlphaByteCount"],[48324,"ImageDataDiscard"],[48325,"AlphaDataDiscard"],[50215,"OceScanjobDesc"],[50216,"OceApplicationSelector"],[50217,"OceIDNumber"],[50218,"OceImageLogic"],[50255,"Annotations"],[50459,"HasselbladExif"],[50547,"OriginalFileName"],[50560,"USPTOOriginalContentType"],[50656,"CR2CFAPattern"],[50710,"CFAPlaneColor"],[50711,"CFALayout"],[50712,"LinearizationTable"],[50713,"BlackLevelRepeatDim"],[50714,"BlackLevel"],[50715,"BlackLevelDeltaH"],[50716,"BlackLevelDeltaV"],[50717,"WhiteLevel"],[50718,"DefaultScale"],[50719,"DefaultCropOrigin"],[50720,"DefaultCropSize"],[50733,"BayerGreenSplit"],[50737,"ChromaBlurRadius"],[50738,"AntiAliasStrength"],[50752,"RawImageSegmentation"],[50780,"BestQualityScale"],[50784,"AliasLayerMetadata"],[50829,"ActiveArea"],[50830,"MaskedAreas"],[50935,"NoiseReductionApplied"],[50974,"SubTileBlockSize"],[50975,"RowInterleaveFactor"],[51008,"OpcodeList1"],[51009,"OpcodeList2"],[51022,"OpcodeList3"],[51041,"NoiseProfile"],[51114,"CacheVersion"],[51125,"DefaultUserCrop"],[51157,"NikonNEFInfo"],[65024,"KdcIFD"]];ae(oe,"ifd0",Yn),ae(oe,"exif",Yn),ie(se,"gps",[[23,{M:"Magnetic North",T:"True North"}],[25,{K:"Kilometers",M:"Miles",N:"Nautical Miles"}]]);var Jn=function(e){a(i,e);var n=d(i);function i(){return t(this,i),n.apply(this,arguments)}return r(i,[{key:"parse",value:function(){return this.parseTags(),this.translate(),this.output}},{key:"parseTags",value:function(){this.raw=b([[0,this.chunk.getUint16(0)],[2,this.chunk.getUint8(2)],[3,this.chunk.getUint16(3)],[5,this.chunk.getUint16(5)],[7,this.chunk.getUint8(7)],[8,this.chunk.getUint8(8)]])}}],[{key:"canHandle",value:function(e,t){return 224===e.getUint8(t+1)&&1246120262===e.getUint32(t+4)&&0===e.getUint8(t+8)}}]),i}(ze);i(Jn,"type","jfif"),i(Jn,"headerLength",9),W.set("jfif",Jn),ie(oe,"jfif",[[0,"JFIFVersion"],[2,"ResolutionUnit"],[3,"XResolution"],[5,"YResolution"],[7,"ThumbnailWidth"],[8,"ThumbnailHeight"]]);var qn=function(e){a(i,e);var n=d(i);function i(){return t(this,i),n.apply(this,arguments)}return r(i,[{key:"parse",value:function(){return this.parseTags(),this.translate(),this.output}},{key:"parseTags",value:function(){this.raw=b([[0,this.chunk.getUint32(0)],[4,this.chunk.getUint32(4)],[8,this.chunk.getUint8(8)],[9,this.chunk.getUint8(9)],[10,this.chunk.getUint8(10)],[11,this.chunk.getUint8(11)],[12,this.chunk.getUint8(12)]].concat(S(this.raw)))}}]),i}(ze);i(qn,"type","ihdr"),W.set("ihdr",qn),ie(oe,"ihdr",[[0,"ImageWidth"],[4,"ImageHeight"],[8,"BitDepth"],[9,"ColorType"],[10,"Compression"],[11,"Filter"],[12,"Interlace"]]),ie(se,"ihdr",[[9,{0:"Grayscale",2:"RGB",3:"Palette",4:"Grayscale with Alpha",6:"RGB with Alpha",DEFAULT:"Unknown"}],[10,{0:"Deflate/Inflate",DEFAULT:"Unknown"}],[11,{0:"Adaptive",DEFAULT:"Unknown"}],[12,{0:"Noninterlaced",1:"Adam7 Interlace",DEFAULT:"Unknown"}]]);var Qn="\0\0\0\0",Zn=function(e){a(i,e);var n=d(i);function i(){return t(this,i),n.apply(this,arguments)}return r(i,[{key:"parse",value:function(){return this.raw=b(),this.parseHeader(),this.parseTags(),this.translate(),this.output}},{key:"parseHeader",value:function(){var e=this.raw;this.chunk.byteLength<84&&F("ICC header is too short");var t=y($n);Array.isArray(t)||("function"==typeof t.entries&&(t=t.entries()),t=S(t));for(var n=0;n<t.length;n++){var r=t[n],i=r[0],a=r[1];i=parseInt(i,10);var o=a(this.chunk,i);o!==Qn&&e.set(i,o)}}},{key:"parseTags",value:function(){for(var e,t,n,r,i,a=this.raw,o=this.chunk.getUint32(128),s=132,u=this.chunk.byteLength;o--;){if(e=this.chunk.getString(s,4),t=this.chunk.getUint32(s+4),n=this.chunk.getUint32(s+8),r=this.chunk.getString(t,4),t+n>u)return void console.warn("reached the end of the first ICC chunk. Enable options.tiff.multiSegment to read all ICC segments.");void 0!==(i=this.parseTag(r,t,n))&&i!==Qn&&a.set(e,i),s+=12}}},{key:"parseTag",value:function(e,t,n){switch(e){case"desc":return this.parseDesc(t);case"mluc":return this.parseMluc(t);case"text":return this.parseText(t,n);case"sig ":return this.parseSig(t)}if(!(t+n>this.chunk.byteLength))return this.chunk.getUint8Array(t,n)}},{key:"parseDesc",value:function(e){var t=this.chunk.getUint32(e+8)-1;return E(this.chunk.getString(e+12,t))}},{key:"parseText",value:function(e,t){return E(this.chunk.getString(e+8,t-8))}},{key:"parseSig",value:function(e){return E(this.chunk.getString(e+8,4))}},{key:"parseMluc",value:function(e){for(var t=this.chunk,n=t.getUint32(e+8),r=t.getUint32(e+12),i=e+16,a=[],o=0;o<n;o++){var s=t.getString(i+0,2),u=t.getString(i+2,2),f=t.getUint32(i+4),c=t.getUint32(i+8)+e,l=E(t.getUnicodeString(c,f));a.push({lang:s,country:u,text:l}),i+=r}return 1===n?a[0].text:a}},{key:"translateValue",value:function(e,t){return"string"==typeof e?t[e]||t[e.toLowerCase()]||e:t[e]||e}}],[{key:"canHandle",value:function(e,t){return 226===e.getUint8(t+1)&&1229144927===e.getUint32(t+4)}},{key:"findPosition",value:function(e,t){var n=p(o(i),"findPosition",this).call(this,e,t);return n.chunkNumber=e.getUint8(t+16),n.chunkCount=e.getUint8(t+17),n.multiSegment=n.chunkCount>1,n}},{key:"handleMultiSegments",value:function(e){return t=function(e){var t=e[0].constructor,n=0,r=e;Array.isArray(r)||("function"==typeof r.entries&&(r=r.entries()),r=S(r));for(var i=0;i<r.length;i++)n+=r[i].length;var a=new t(n),o=0,s=e;Array.isArray(s)||("function"==typeof s.entries&&(s=s.entries()),s=S(s));for(var u=0;u<s.length;u++){var f=s[u];a.set(f,o),o+=f.length}return a}(e.map((function(e){return e.chunk.toUint8()}))),new V(t);var t}}]),i}(ze);i(Zn,"type","icc"),i(Zn,"multiSegment",!0),i(Zn,"headerLength",18);var $n={4:er,8:function(e,t){return[e.getUint8(t),e.getUint8(t+1)>>4,e.getUint8(t+1)%16].map((function(e){return e.toString(10)})).join(".")},12:er,16:er,20:er,24:function(e,t){var n=e.getUint16(t),r=e.getUint16(t+2)-1,i=e.getUint16(t+4),a=e.getUint16(t+6),o=e.getUint16(t+8),s=e.getUint16(t+10);return new Date(Date.UTC(n,r,i,a,o,s))},36:er,40:er,48:er,52:er,64:function(e,t){return e.getUint32(t)},80:er};function er(e,t){return E(e.getString(t,4))}W.set("icc",Zn),ie(oe,"icc",[[4,"ProfileCMMType"],[8,"ProfileVersion"],[12,"ProfileClass"],[16,"ColorSpaceData"],[20,"ProfileConnectionSpace"],[24,"ProfileDateTime"],[36,"ProfileFileSignature"],[40,"PrimaryPlatform"],[44,"CMMFlags"],[48,"DeviceManufacturer"],[52,"DeviceModel"],[56,"DeviceAttributes"],[64,"RenderingIntent"],[68,"ConnectionSpaceIlluminant"],[80,"ProfileCreator"],[84,"ProfileID"],["Header","ProfileHeader"],["MS00","WCSProfiles"],["bTRC","BlueTRC"],["bXYZ","BlueMatrixColumn"],["bfd","UCRBG"],["bkpt","MediaBlackPoint"],["calt","CalibrationDateTime"],["chad","ChromaticAdaptation"],["chrm","Chromaticity"],["ciis","ColorimetricIntentImageState"],["clot","ColorantTableOut"],["clro","ColorantOrder"],["clrt","ColorantTable"],["cprt","ProfileCopyright"],["crdi","CRDInfo"],["desc","ProfileDescription"],["devs","DeviceSettings"],["dmdd","DeviceModelDesc"],["dmnd","DeviceMfgDesc"],["dscm","ProfileDescriptionML"],["fpce","FocalPlaneColorimetryEstimates"],["gTRC","GreenTRC"],["gXYZ","GreenMatrixColumn"],["gamt","Gamut"],["kTRC","GrayTRC"],["lumi","Luminance"],["meas","Measurement"],["meta","Metadata"],["mmod","MakeAndModel"],["ncl2","NamedColor2"],["ncol","NamedColor"],["ndin","NativeDisplayInfo"],["pre0","Preview0"],["pre1","Preview1"],["pre2","Preview2"],["ps2i","PS2RenderingIntent"],["ps2s","PostScript2CSA"],["psd0","PostScript2CRD0"],["psd1","PostScript2CRD1"],["psd2","PostScript2CRD2"],["psd3","PostScript2CRD3"],["pseq","ProfileSequenceDesc"],["psid","ProfileSequenceIdentifier"],["psvm","PS2CRDVMSize"],["rTRC","RedTRC"],["rXYZ","RedMatrixColumn"],["resp","OutputResponse"],["rhoc","ReflectionHardcopyOrigColorimetry"],["rig0","PerceptualRenderingIntentGamut"],["rig2","SaturationRenderingIntentGamut"],["rpoc","ReflectionPrintOutputColorimetry"],["sape","SceneAppearanceEstimates"],["scoe","SceneColorimetryEstimates"],["scrd","ScreeningDesc"],["scrn","Screening"],["targ","CharTarget"],["tech","Technology"],["vcgt","VideoCardGamma"],["view","ViewingConditions"],["vued","ViewingCondDesc"],["wtpt","MediaWhitePoint"]]);var tr={"4d2p":"Erdt Systems",AAMA:"Aamazing Technologies",ACER:"Acer",ACLT:"Acolyte Color Research",ACTI:"Actix Sytems",ADAR:"Adara Technology",ADBE:"Adobe",ADI:"ADI Systems",AGFA:"Agfa Graphics",ALMD:"Alps Electric",ALPS:"Alps Electric",ALWN:"Alwan Color Expertise",AMTI:"Amiable Technologies",AOC:"AOC International",APAG:"Apago",APPL:"Apple Computer",AST:"AST","AT&T":"AT&T",BAEL:"BARBIERI electronic",BRCO:"Barco NV",BRKP:"Breakpoint",BROT:"Brother",BULL:"Bull",BUS:"Bus Computer Systems","C-IT":"C-Itoh",CAMR:"Intel",CANO:"Canon",CARR:"Carroll Touch",CASI:"Casio",CBUS:"Colorbus PL",CEL:"Crossfield",CELx:"Crossfield",CGS:"CGS Publishing Technologies International",CHM:"Rochester Robotics",CIGL:"Colour Imaging Group, London",CITI:"Citizen",CL00:"Candela",CLIQ:"Color IQ",CMCO:"Chromaco",CMiX:"CHROMiX",COLO:"Colorgraphic Communications",COMP:"Compaq",COMp:"Compeq/Focus Technology",CONR:"Conrac Display Products",CORD:"Cordata Technologies",CPQ:"Compaq",CPRO:"ColorPro",CRN:"Cornerstone",CTX:"CTX International",CVIS:"ColorVision",CWC:"Fujitsu Laboratories",DARI:"Darius Technology",DATA:"Dataproducts",DCP:"Dry Creek Photo",DCRC:"Digital Contents Resource Center, Chung-Ang University",DELL:"Dell Computer",DIC:"Dainippon Ink and Chemicals",DICO:"Diconix",DIGI:"Digital","DL&C":"Digital Light & Color",DPLG:"Doppelganger",DS:"Dainippon Screen",DSOL:"DOOSOL",DUPN:"DuPont",EPSO:"Epson",ESKO:"Esko-Graphics",ETRI:"Electronics and Telecommunications Research Institute",EVER:"Everex Systems",EXAC:"ExactCODE",Eizo:"Eizo",FALC:"Falco Data Products",FF:"Fuji Photo Film",FFEI:"FujiFilm Electronic Imaging",FNRD:"Fnord Software",FORA:"Fora",FORE:"Forefront Technology",FP:"Fujitsu",FPA:"WayTech Development",FUJI:"Fujitsu",FX:"Fuji Xerox",GCC:"GCC Technologies",GGSL:"Global Graphics Software",GMB:"Gretagmacbeth",GMG:"GMG",GOLD:"GoldStar Technology",GOOG:"Google",GPRT:"Giantprint",GTMB:"Gretagmacbeth",GVC:"WayTech Development",GW2K:"Sony",HCI:"HCI",HDM:"Heidelberger Druckmaschinen",HERM:"Hermes",HITA:"Hitachi America",HP:"Hewlett-Packard",HTC:"Hitachi",HiTi:"HiTi Digital",IBM:"IBM",IDNT:"Scitex",IEC:"Hewlett-Packard",IIYA:"Iiyama North America",IKEG:"Ikegami Electronics",IMAG:"Image Systems",IMI:"Ingram Micro",INTC:"Intel",INTL:"N/A (INTL)",INTR:"Intra Electronics",IOCO:"Iocomm International Technology",IPS:"InfoPrint Solutions Company",IRIS:"Scitex",ISL:"Ichikawa Soft Laboratory",ITNL:"N/A (ITNL)",IVM:"IVM",IWAT:"Iwatsu Electric",Idnt:"Scitex",Inca:"Inca Digital Printers",Iris:"Scitex",JPEG:"Joint Photographic Experts Group",JSFT:"Jetsoft Development",JVC:"JVC Information Products",KART:"Scitex",KFC:"KFC Computek Components",KLH:"KLH Computers",KMHD:"Konica Minolta",KNCA:"Konica",KODA:"Kodak",KYOC:"Kyocera",Kart:"Scitex",LCAG:"Leica",LCCD:"Leeds Colour",LDAK:"Left Dakota",LEAD:"Leading Technology",LEXM:"Lexmark International",LINK:"Link Computer",LINO:"Linotronic",LITE:"Lite-On",Leaf:"Leaf",Lino:"Linotronic",MAGC:"Mag Computronic",MAGI:"MAG Innovision",MANN:"Mannesmann",MICN:"Micron Technology",MICR:"Microtek",MICV:"Microvitec",MINO:"Minolta",MITS:"Mitsubishi Electronics America",MITs:"Mitsuba",MNLT:"Minolta",MODG:"Modgraph",MONI:"Monitronix",MONS:"Monaco Systems",MORS:"Morse Technology",MOTI:"Motive Systems",MSFT:"Microsoft",MUTO:"MUTOH INDUSTRIES",Mits:"Mitsubishi Electric",NANA:"NANAO",NEC:"NEC",NEXP:"NexPress Solutions",NISS:"Nissei Sangyo America",NKON:"Nikon",NONE:"none",OCE:"Oce Technologies",OCEC:"OceColor",OKI:"Oki",OKID:"Okidata",OKIP:"Okidata",OLIV:"Olivetti",OLYM:"Olympus",ONYX:"Onyx Graphics",OPTI:"Optiquest",PACK:"Packard Bell",PANA:"Matsushita Electric Industrial",PANT:"Pantone",PBN:"Packard Bell",PFU:"PFU",PHIL:"Philips Consumer Electronics",PNTX:"HOYA",POne:"Phase One A/S",PREM:"Premier Computer Innovations",PRIN:"Princeton Graphic Systems",PRIP:"Princeton Publishing Labs",QLUX:"Hong Kong",QMS:"QMS",QPCD:"QPcard AB",QUAD:"QuadLaser",QUME:"Qume",RADI:"Radius",RDDx:"Integrated Color Solutions",RDG:"Roland DG",REDM:"REDMS Group",RELI:"Relisys",RGMS:"Rolf Gierling Multitools",RICO:"Ricoh",RNLD:"Edmund Ronald",ROYA:"Royal",RPC:"Ricoh Printing Systems",RTL:"Royal Information Electronics",SAMP:"Sampo",SAMS:"Samsung",SANT:"Jaime Santana Pomares",SCIT:"Scitex",SCRN:"Dainippon Screen",SDP:"Scitex",SEC:"Samsung",SEIK:"Seiko Instruments",SEIk:"Seikosha",SGUY:"ScanGuy.com",SHAR:"Sharp Laboratories",SICC:"International Color Consortium",SONY:"Sony",SPCL:"SpectraCal",STAR:"Star",STC:"Sampo Technology",Scit:"Scitex",Sdp:"Scitex",Sony:"Sony",TALO:"Talon Technology",TAND:"Tandy",TATU:"Tatung",TAXA:"TAXAN America",TDS:"Tokyo Denshi Sekei",TECO:"TECO Information Systems",TEGR:"Tegra",TEKT:"Tektronix",TI:"Texas Instruments",TMKR:"TypeMaker",TOSB:"Toshiba",TOSH:"Toshiba",TOTK:"TOTOKU ELECTRIC",TRIU:"Triumph",TSBT:"Toshiba",TTX:"TTX Computer Products",TVM:"TVM Professional Monitor",TW:"TW Casper",ULSX:"Ulead Systems",UNIS:"Unisys",UTZF:"Utz Fehlau & Sohn",VARI:"Varityper",VIEW:"Viewsonic",VISL:"Visual communication",VIVO:"Vivo Mobile Communication",WANG:"Wang",WLBR:"Wilbur Imaging",WTG2:"Ware To Go",WYSE:"WYSE Technology",XERX:"Xerox",XRIT:"X-Rite",ZRAN:"Zoran",Zebr:"Zebra Technologies",appl:"Apple Computer",bICC:"basICColor",berg:"bergdesign",ceyd:"Integrated Color Solutions",clsp:"MacDermid ColorSpan",ds:"Dainippon Screen",dupn:"DuPont",ffei:"FujiFilm Electronic Imaging",flux:"FluxData",iris:"Scitex",kart:"Scitex",lcms:"Little CMS",lino:"Linotronic",none:"none",ob4d:"Erdt Systems",obic:"Medigraph",quby:"Qubyx Sarl",scit:"Scitex",scrn:"Dainippon Screen",sdp:"Scitex",siwi:"SIWI GRAFIKA",yxym:"YxyMaster"},nr={scnr:"Scanner",mntr:"Monitor",prtr:"Printer",link:"Device Link",abst:"Abstract",spac:"Color Space Conversion Profile",nmcl:"Named Color",cenc:"ColorEncodingSpace profile",mid:"MultiplexIdentification profile",mlnk:"MultiplexLink profile",mvis:"MultiplexVisualization profile",nkpf:"Nikon Input Device Profile (NON-STANDARD!)"};ie(se,"icc",[[4,tr],[12,nr],[40,m({},tr,nr)],[48,tr],[80,tr],[64,{0:"Perceptual",1:"Relative Colorimetric",2:"Saturation",3:"Absolute Colorimetric"}],["tech",{amd:"Active Matrix Display",crt:"Cathode Ray Tube Display",kpcd:"Photo CD",pmd:"Passive Matrix Display",dcam:"Digital Camera",dcpj:"Digital Cinema Projector",dmpc:"Digital Motion Picture Camera",dsub:"Dye Sublimation Printer",epho:"Electrophotographic Printer",esta:"Electrostatic Printer",flex:"Flexography",fprn:"Film Writer",fscn:"Film Scanner",grav:"Gravure",ijet:"Ink Jet Printer",imgs:"Photo Image Setter",mpfr:"Motion Picture Film Recorder",mpfs:"Motion Picture Film Scanner",offs:"Offset Lithography",pjtv:"Projection Television",rpho:"Photographic Paper Printer",rscn:"Reflective Scanner",silk:"Silkscreen",twax:"Thermal Wax Printer",vidc:"Video Camera",vidm:"Video Monitor"}]]);var rr=function(e){a(i,e);var n=d(i);function i(){return t(this,i),n.apply(this,arguments)}return r(i,[{key:"parse",value:function(){for(var e=this.raw,t=this.chunk.byteLength-1,n=!1,r=0;r<t;r++)if(28===this.chunk.getUint8(r)&&2===this.chunk.getUint8(r+1)){n=!0;var i=this.chunk.getUint16(r+3),a=this.chunk.getUint8(r+2),o=this.chunk.getLatin1String(r+5,i);e.set(a,this.pluralizeValue(e.get(a),o)),r+=4+i}else if(n)break;return this.translate(),this.output}},{key:"pluralizeValue",value:function(e,t){return void 0!==e?e instanceof Array?(e.push(t),e):[e,t]:t}}],[{key:"canHandle",value:function(e,t,n){return 237===e.getUint8(t+1)&&"Photoshop"===e.getString(t+4,9)&&void 0!==this.containsIptc8bim(e,t,n)}},{key:"headerLength",value:function(e,t,n){var r,i=this.containsIptc8bim(e,t,n);if(void 0!==i)return(r=e.getUint8(t+i+7))%2!=0&&(r+=1),0===r&&(r=4),i+8+r}},{key:"containsIptc8bim",value:function(e,t,n){for(var r=0;r<n;r++)if(this.isIptcSegmentHead(e,t+r))return r}},{key:"isIptcSegmentHead",value:function(e,t){return 56===e.getUint8(t)&&943868237===e.getUint32(t)&&1028===e.getUint16(t+4)}}]),i}(ze);i(rr,"type","iptc"),i(rr,"translateValues",!1),i(rr,"reviveValues",!1),W.set("iptc",rr),ie(oe,"iptc",[[0,"ApplicationRecordVersion"],[3,"ObjectTypeReference"],[4,"ObjectAttributeReference"],[5,"ObjectName"],[7,"EditStatus"],[8,"EditorialUpdate"],[10,"Urgency"],[12,"SubjectReference"],[15,"Category"],[20,"SupplementalCategories"],[22,"FixtureIdentifier"],[25,"Keywords"],[26,"ContentLocationCode"],[27,"ContentLocationName"],[30,"ReleaseDate"],[35,"ReleaseTime"],[37,"ExpirationDate"],[38,"ExpirationTime"],[40,"SpecialInstructions"],[42,"ActionAdvised"],[45,"ReferenceService"],[47,"ReferenceDate"],[50,"ReferenceNumber"],[55,"DateCreated"],[60,"TimeCreated"],[62,"DigitalCreationDate"],[63,"DigitalCreationTime"],[65,"OriginatingProgram"],[70,"ProgramVersion"],[75,"ObjectCycle"],[80,"Byline"],[85,"BylineTitle"],[90,"City"],[92,"Sublocation"],[95,"State"],[100,"CountryCode"],[101,"Country"],[103,"OriginalTransmissionReference"],[105,"Headline"],[110,"Credit"],[115,"Source"],[116,"CopyrightNotice"],[118,"Contact"],[120,"Caption"],[121,"LocalCaption"],[122,"Writer"],[125,"RasterizedCaption"],[130,"ImageType"],[131,"ImageOrientation"],[135,"LanguageIdentifier"],[150,"AudioType"],[151,"AudioSamplingRate"],[152,"AudioSamplingResolution"],[153,"AudioDuration"],[154,"AudioOutcue"],[184,"JobID"],[185,"MasterDocumentID"],[186,"ShortDocumentID"],[187,"UniqueDocumentID"],[188,"OwnerID"],[200,"ObjectPreviewFileFormat"],[201,"ObjectPreviewFileVersion"],[202,"ObjectPreviewData"],[221,"Prefs"],[225,"ClassifyState"],[228,"SimilarityIndex"],[230,"DocumentNotes"],[231,"DocumentHistory"],[232,"ExifCameraInfo"],[255,"CatalogSets"]]),ie(se,"iptc",[[10,{0:"0 (reserved)",1:"1 (most urgent)",2:"2",3:"3",4:"4",5:"5 (normal urgency)",6:"6",7:"7",8:"8 (least urgent)",9:"9 (user-defined priority)"}],[75,{a:"Morning",b:"Both Morning and Evening",p:"Evening"}],[131,{L:"Landscape",P:"Portrait",S:"Square"}]]),e.Exifr=Be,e.Options=we,e.allFormatters=Ae,e.chunkedProps=ye,e.createDictionary=ie,e.default=bn,e.extendDictionary=ae,e.fetchUrlAsArrayBuffer=te,e.fileParsers=_,e.fileReaders=K,e.gps=lt,e.gpsOnlyOptions=ht,e.inheritables=be,e.orientation=St,e.orientationOnlyOptions=Ct,e.otherSegments=me,e.parse=Ne,e.readBlobAsArrayBuffer=ne,e.rotation=kt,e.rotations=Pt,e.segmentParsers=W,e.segments=ke,e.segmentsAndBlocks=Ce,e.sidecar=Mn,e.tagKeys=oe,e.tagRevivers=ue,e.tagValues=se,e.thumbnail=vt,e.thumbnailOnlyOptions=yt,e.thumbnailUrl=gt,e.tiffBlocks=Se,e.tiffExtractables=Pe,Object.defineProperty(e,"__esModule",{value:!0})}));
