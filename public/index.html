<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EXIF 图片处理器</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>📸 EXIF 图片处理器</h1>
            <p>上传图片，提取EXIF信息并生成带有信息叠加的新图片</p>
        </header>

        <main>
            <!-- 上传区域 -->
            <section class="upload-section">
                <div class="upload-area" id="uploadArea">
                    <div class="upload-content">
                        <div class="upload-icon">📁</div>
                        <h3>选择或拖拽图片文件</h3>
                        <p>支持 JPG, PNG, TIFF 等格式，最大 10MB</p>
                        <input type="file" id="imageInput" accept="image/*" hidden>
                        <button type="button" class="upload-btn" onclick="document.getElementById('imageInput').click()">
                            选择文件
                        </button>
                    </div>
                </div>
            </section>

            <!-- 加载状态 -->
            <section class="loading-section" id="loadingSection" style="display: none;">
                <div class="loading-spinner"></div>
                <p>正在处理图片...</p>
            </section>

            <!-- 结果展示区域 -->
            <section class="result-section" id="resultSection" style="display: none;">
                <div class="result-grid">
                    <!-- 原图预览 -->
                    <div class="image-preview">
                        <h3>原始图片</h3>
                        <img id="originalImage" alt="原始图片">
                    </div>

                    <!-- EXIF信息 -->
                    <div class="exif-info">
                        <h3>📊 EXIF 信息</h3>
                        <div id="exifData" class="exif-data"></div>
                    </div>

                    <!-- 处理后的图片 -->
                    <div class="processed-image">
                        <h3>处理后的图片</h3>
                        <img id="processedImage" alt="处理后的图片">
                        <div class="download-section">
                            <button id="downloadBtn" class="download-btn">
                                💾 下载图片
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 错误提示 -->
            <section class="error-section" id="errorSection" style="display: none;">
                <div class="error-message">
                    <span class="error-icon">❌</span>
                    <span id="errorText"></span>
                </div>
            </section>
        </main>

        <footer>
            <p>© 2024 EXIF 图片处理器 - 轻松提取和展示图片元数据</p>
        </footer>
    </div>

    <script src="script.js"></script>
</body>
</html>
