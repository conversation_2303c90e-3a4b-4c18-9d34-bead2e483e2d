* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* 上传区域 */
.upload-section {
    margin-bottom: 30px;
}

.upload-area {
    background: white;
    border: 3px dashed #ddd;
    border-radius: 15px;
    padding: 60px 20px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: #667eea;
    background: #f8f9ff;
}

.upload-area.dragover {
    border-color: #667eea;
    background: #f0f4ff;
    transform: scale(1.02);
}

.upload-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

.upload-content h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: #333;
}

.upload-content p {
    color: #666;
    margin-bottom: 20px;
}

.upload-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-size: 1rem;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.upload-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* 加载状态 */
.loading-section {
    text-align: center;
    padding: 40px;
    background: white;
    border-radius: 15px;
    margin-bottom: 30px;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 结果展示区域 */
.result-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.result-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.processed-image {
    grid-column: 1 / -1;
}

.image-preview, .exif-info, .processed-image {
    text-align: center;
}

.image-preview h3, .exif-info h3, .processed-image h3 {
    margin-bottom: 20px;
    color: #333;
    font-size: 1.3rem;
}

.image-preview img, .processed-image img {
    max-width: 100%;
    height: auto;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

/* EXIF信息样式 */
.exif-data {
    text-align: left;
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
}

.exif-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.exif-item:last-child {
    border-bottom: none;
}

.exif-label {
    font-weight: 600;
    color: #555;
}

.exif-value {
    color: #333;
    font-family: 'Courier New', monospace;
}

/* 下载按钮 */
.download-section {
    margin-top: 20px;
}

.download-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.download-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
}

/* 错误提示 */
.error-section {
    background: #fff5f5;
    border: 2px solid #fed7d7;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 30px;
}

.error-message {
    display: flex;
    align-items: center;
    color: #c53030;
    font-size: 1.1rem;
}

.error-icon {
    margin-right: 10px;
    font-size: 1.5rem;
}

/* 页脚 */
footer {
    text-align: center;
    color: white;
    opacity: 0.8;
    margin-top: 40px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .result-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .upload-area {
        padding: 40px 20px;
    }
    
    .upload-icon {
        font-size: 3rem;
    }
}
