const sharp = require('sharp');
const fs = require('fs');

// 创建一个简单的测试图片（不包含EXIF，我们将使用现有的图片进行测试）
async function createTestImage() {
    try {
        // 创建一个简单的测试图片
        await sharp({
            create: {
                width: 800,
                height: 600,
                channels: 3,
                background: { r: 100, g: 150, b: 200 }
            }
        })
        .png()
        .toFile('simple-test.png');

        console.log('简单测试图片已创建: simple-test.png');
    } catch (error) {
        console.error('创建测试图片失败:', error);
    }
}

createTestImage();
