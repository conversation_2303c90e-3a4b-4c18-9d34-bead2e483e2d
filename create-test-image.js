const sharp = require('sharp');
const fs = require('fs');

// 创建一个带有EXIF信息的测试图片
async function createTestImage() {
    try {
        // 创建一个简单的测试图片
        const testImage = await sharp({
            create: {
                width: 800,
                height: 600,
                channels: 3,
                background: { r: 100, g: 150, b: 200 }
            }
        })
        .jpeg()
        .toBuffer();

        // 创建EXIF数据
        const exifData = {
            '0th': {
                [sharp.constants.TAGS.Make]: 'Apple',
                [sharp.constants.TAGS.Model]: 'iPhone 14 Pro Max',
                [sharp.constants.TAGS.DateTime]: '2024:06:30 14:30:00',
                [sharp.constants.TAGS.XResolution]: [72, 1],
                [sharp.constants.TAGS.YResolution]: [72, 1],
                [sharp.constants.TAGS.ResolutionUnit]: 2
            },
            'Exif': {
                [sharp.constants.TAGS.DateTimeOriginal]: '2024:06:30 14:30:00',
                [sharp.constants.TAGS.ExposureTime]: [1, 40],
                [sharp.constants.TAGS.FNumber]: [178, 100],
                [sharp.constants.TAGS.ISO]: 800,
                [sharp.constants.TAGS.FocalLength]: [686, 100]
            },
            'GPS': {
                [sharp.constants.TAGS.GPSLatitudeRef]: 'N',
                [sharp.constants.TAGS.GPSLatitude]: [[22, 1], [53, 1], [1917, 100]],
                [sharp.constants.TAGS.GPSLongitudeRef]: 'E',
                [sharp.constants.TAGS.GPSLongitude]: [[113, 1], [12, 1], [1751, 100]]
            }
        };

        // 保存测试图片
        await sharp(testImage)
            .jpeg({ quality: 90 })
            .toFile('test-image.jpg');

        console.log('测试图片已创建: test-image.jpg');
    } catch (error) {
        console.error('创建测试图片失败:', error);
    }
}

createTestImage();
